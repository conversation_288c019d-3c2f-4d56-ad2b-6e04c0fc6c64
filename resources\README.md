# Resources Directory

This directory contains application resources including:

## Directory Structure

- `icons/` - Application icons and UI icons
- `styles/` - CSS/QSS style files for UI theming
- `models/` - PaddleOCR model files (downloaded automatically)

## Model Files

The application will automatically download required models to the `models/` directory on first run:

- OCR detection models
- OCR recognition models  
- Text direction classification models
- Table recognition models
- Layout analysis models
- Translation models

## Icons

Place application icons in the `icons/` directory:
- `app_icon.png` - Main application icon
- Other UI icons as needed

## Styles

Custom Qt stylesheets can be placed in the `styles/` directory for UI theming.
