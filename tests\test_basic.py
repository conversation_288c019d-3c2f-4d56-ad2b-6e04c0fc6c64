#!/usr/bin/env python3
"""
基础功能测试脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        # 测试配置模块
        from config.settings import AppSettings
        from config.models_config import ModelsConfig
        print("✓ 配置模块导入成功")
        
        # 测试工具模块
        from utils.file_utils import FileUtils
        from utils.image_utils import ImageUtils
        from utils.system_monitor import SystemMonitor
        print("✓ 工具模块导入成功")
        
        # 测试核心模块
        from core.ocr_engine import OCREngine
        print("✓ 核心模块导入成功")
        
        # 测试GUI模块
        from gui.main_window import MainWindow
        from gui.widgets.file_input import FileInputWidget
        from gui.widgets.mode_selector import ModeSelectorWidget
        from gui.widgets.progress_panel import ProgressPanelWidget
        from gui.widgets.resource_monitor import ResourceMonitorWidget
        print("✓ GUI模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"✗ 模块导入失败: {e}")
        return False

def test_dependencies():
    """测试依赖包"""
    print("\n测试依赖包...")
    
    dependencies = [
        ("PyQt6", "PyQt6"),
        ("PaddlePaddle", "paddle"),
        ("PaddleOCR", "paddleocr"),
        ("PIL", "PIL"),
        ("OpenCV", "cv2"),
        ("NumPy", "numpy"),
        ("psutil", "psutil"),
    ]
    
    missing_deps = []
    
    for name, module in dependencies:
        try:
            __import__(module)
            print(f"✓ {name} 可用")
        except ImportError:
            print(f"✗ {name} 不可用")
            missing_deps.append(name)
    
    return len(missing_deps) == 0

def test_configuration():
    """测试配置系统"""
    print("\n测试配置系统...")
    
    try:
        from config.settings import AppSettings
        from config.models_config import ModelsConfig
        
        # 测试应用设置
        settings = AppSettings()
        print(f"✓ 应用设置加载成功")
        print(f"  - OCR语言: {settings.ocr.lang}")
        print(f"  - 使用GPU: {settings.ocr.use_gpu}")
        print(f"  - 界面主题: {settings.ui.theme}")
        
        # 测试模型配置
        models_config = ModelsConfig()
        all_models = models_config.get_all_models()
        print(f"✓ 模型配置加载成功，共 {len(all_models)} 个模型")
        
        missing_models = models_config.get_missing_models()
        if missing_models:
            print(f"  - 缺少 {len(missing_models)} 个模型文件")
            total_size = models_config.get_total_download_size()
            print(f"  - 需要下载约 {total_size:.1f} MB")
        else:
            print("  - 所有模型文件已就绪")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置系统测试失败: {e}")
        return False

def test_file_utils():
    """测试文件工具"""
    print("\n测试文件工具...")
    
    try:
        from utils.file_utils import FileUtils
        
        # 测试文件格式检查
        test_files = [
            ("test.pdf", True),
            ("test.png", True),
            ("test.jpg", True),
            ("test.txt", False),
            ("test.doc", False),
        ]
        
        for filename, expected in test_files:
            result = FileUtils.is_supported_input_file(filename)
            if result == expected:
                print(f"✓ {filename} 格式检查正确")
            else:
                print(f"✗ {filename} 格式检查错误")
        
        # 测试临时目录
        temp_dir = FileUtils.get_temp_directory()
        print(f"✓ 临时目录: {temp_dir}")
        
        return True
        
    except Exception as e:
        print(f"✗ 文件工具测试失败: {e}")
        return False

def test_system_monitor():
    """测试系统监控"""
    print("\n测试系统监控...")
    
    try:
        from utils.system_monitor import SystemMonitor
        
        monitor = SystemMonitor()
        
        # 测试系统资源获取
        resources = monitor.get_system_resources()
        print(f"✓ CPU使用率: {resources['cpu_percent']:.1f}%")
        print(f"✓ 内存使用率: {resources['memory_percent']:.1f}%")
        
        if monitor.is_gpu_available():
            print(f"✓ GPU使用率: {resources['gpu_percent']:.1f}%")
            gpu_info = monitor.get_gpu_info()
            if gpu_info:
                print(f"  - GPU: {gpu_info['name']}")
        else:
            print("- GPU不可用")
        
        # 测试CPU核心数
        cpu_count = monitor.get_cpu_count()
        print(f"✓ CPU核心数: {cpu_count}")
        
        return True
        
    except Exception as e:
        print(f"✗ 系统监控测试失败: {e}")
        return False

def test_gui_creation():
    """测试GUI创建（不显示）"""
    print("\n测试GUI创建...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from config.settings import AppSettings
        from utils.system_monitor import SystemMonitor
        from gui.main_window import MainWindow
        
        # 创建应用程序实例
        app = QApplication([])
        
        # 创建设置和监控器
        settings = AppSettings()
        system_monitor = SystemMonitor()
        
        # 创建主窗口（但不显示）
        main_window = MainWindow(settings, system_monitor)
        
        print("✓ 主窗口创建成功")
        print(f"  - 窗口标题: {main_window.windowTitle()}")
        print(f"  - 窗口大小: {main_window.size().width()}x{main_window.size().height()}")
        
        # 清理
        main_window.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"✗ GUI创建测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("Prisma OCR & Translator - 基础功能测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("依赖包", test_dependencies),
        ("配置系统", test_configuration),
        ("文件工具", test_file_utils),
        ("系统监控", test_system_monitor),
        ("GUI创建", test_gui_creation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！应用程序基础功能正常。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查相关问题。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
