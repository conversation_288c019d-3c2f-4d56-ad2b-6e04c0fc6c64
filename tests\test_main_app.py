#!/usr/bin/env python3
"""
主应用程序测试 - 测试完整的应用程序启动流程
"""

import sys
import os
import time
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def test_main_import():
    """测试主程序导入"""
    print("测试主程序导入...")
    
    try:
        # 测试main.py的导入
        import main
        print("✅ main.py 导入成功")
        
        # 检查主要函数
        if hasattr(main, 'main'):
            print("✅ main() 函数存在")
        else:
            print("❌ main() 函数不存在")
            return False
        
        if hasattr(main, 'check_dependencies'):
            print("✅ check_dependencies() 函数存在")
        else:
            print("❌ check_dependencies() 函数不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 主程序导入失败: {e}")
        return False


def test_dependency_check():
    """测试依赖检查功能"""
    print("\n测试依赖检查功能...")
    
    try:
        import main
        
        # 调用依赖检查函数
        result = main.check_dependencies()
        
        if result:
            print("✅ 依赖检查通过")
        else:
            print("⚠️  依赖检查未完全通过（可能缺少某些包）")
        
        return True
        
    except Exception as e:
        print(f"❌ 依赖检查失败: {e}")
        return False


def test_web_controller_startup():
    """测试Web控制器启动"""
    print("\n测试Web控制器启动...")
    
    try:
        from webui.web_controller import WebController
        
        # 创建Web控制器（调试模式，不启动窗口）
        web_controller = WebController(debug=True)
        print("✅ Web控制器创建成功")
        
        # 检查API桥接器
        if hasattr(web_controller, 'api_bridge'):
            print("✅ API桥接器已初始化")
        
        # 检查是否有启动方法
        if hasattr(web_controller, 'start_application'):
            print("✅ start_application() 方法存在")
        else:
            print("❌ start_application() 方法不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Web控制器启动测试失败: {e}")
        return False


def test_config_loading():
    """测试配置加载"""
    print("\n测试配置加载...")
    
    try:
        from config.settings import AppSettings
        from config.models_config import ModelsConfig
        
        # 加载应用设置
        settings = AppSettings()
        print("✅ 应用设置加载成功")
        print(f"  - OCR语言: {settings.ocr.lang}")
        print(f"  - 使用GPU: {settings.ocr.use_gpu}")
        
        # 加载模型配置
        models_config = ModelsConfig()
        all_models = models_config.get_all_models()
        print(f"✅ 模型配置加载成功，共 {len(all_models)} 个模型类型")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False


def test_core_ocr_engine():
    """测试核心OCR引擎"""
    print("\n测试核心OCR引擎...")
    
    try:
        from core.ocr_engine import OCREngine
        from config.settings import AppSettings
        from config.models_config import ModelsConfig

        # 创建设置
        settings = AppSettings()
        models_config = ModelsConfig()

        # 创建OCR引擎
        ocr_engine = OCREngine(settings.ocr, models_config)
        print("✅ OCR引擎创建成功")
        
        # 检查OCR引擎方法
        if hasattr(ocr_engine, 'process_images'):
            print("✅ process_images() 方法存在")
        else:
            print("❌ process_images() 方法不存在")
        
        if hasattr(ocr_engine, 'initialize_ocr'):
            print("✅ initialize_ocr() 方法存在")
        else:
            print("❌ initialize_ocr() 方法不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ OCR引擎测试失败: {e}")
        return False


def test_file_utilities():
    """测试文件工具"""
    print("\n测试文件工具...")
    
    try:
        from utils.file_utils import FileUtils
        
        # 测试PDF文件检查
        test_pdf = project_root / "ocr测试.pdf"
        if test_pdf.exists():
            is_supported = FileUtils.is_supported_input_file(test_pdf)
            print(f"✅ PDF文件支持检查: {is_supported}")
            
            is_pdf = FileUtils.is_pdf_file(test_pdf)
            print(f"✅ PDF文件类型检查: {is_pdf}")
            
            file_size = FileUtils.get_file_size_mb(test_pdf)
            print(f"✅ 文件大小: {file_size:.2f} MB")
        else:
            print("⚠️  测试PDF文件不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件工具测试失败: {e}")
        return False


def test_system_monitor():
    """测试系统监控"""
    print("\n测试系统监控...")
    
    try:
        from utils.system_monitor import SystemMonitor
        
        # 创建系统监控器
        monitor = SystemMonitor()
        print("✅ 系统监控器创建成功")
        
        # 获取系统资源信息
        resources = monitor.get_system_resources()
        print(f"✅ CPU使用率: {resources['cpu_percent']:.1f}%")
        print(f"✅ 内存使用率: {resources['memory_percent']:.1f}%")
        
        if monitor.is_gpu_available():
            print(f"✅ GPU使用率: {resources['gpu_percent']:.1f}%")
        else:
            print("⚠️  GPU不可用")
        
        return True
        
    except Exception as e:
        print(f"❌ 系统监控测试失败: {e}")
        return False


def test_dry_run_startup():
    """测试应用程序干运行启动"""
    print("\n测试应用程序干运行启动...")
    
    try:
        # 尝试导入并调用主程序的设置函数
        import main
        
        # 测试日志设置
        main.setup_logging()
        print("✅ 日志系统设置成功")
        
        # 测试依赖检查
        deps_ok = main.check_dependencies()
        if deps_ok:
            print("✅ 依赖检查通过")
        else:
            print("⚠️  依赖检查有警告")
        
        # 创建Web控制器但不启动
        from webui.web_controller import WebController
        web_controller = WebController(debug=True)
        print("✅ Web控制器创建成功（未启动窗口）")
        
        return True
        
    except Exception as e:
        print(f"❌ 干运行启动失败: {e}")
        return False


def main():
    """主测试函数"""
    print("Prisma OCR & Translator - 主应用程序测试")
    print("=" * 60)
    
    tests = [
        ("主程序导入", test_main_import),
        ("依赖检查", test_dependency_check),
        ("Web控制器", test_web_controller_startup),
        ("配置加载", test_config_loading),
        ("OCR引擎", test_core_ocr_engine),
        ("文件工具", test_file_utilities),
        ("系统监控", test_system_monitor),
        ("干运行启动", test_dry_run_startup),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print(f"\n{'='*60}")
    print(f"主应用程序测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！应用程序可以正常启动")
        print("\n建议操作:")
        print("1. 启动应用程序: python main.py")
        print("2. 调试模式启动: python main.py --debug")
        return 0
    elif passed >= total * 0.8:
        print("⚠️  大部分测试通过，应用程序基本可用")
        print("\n建议操作:")
        print("1. 检查失败的测试项")
        print("2. 尝试启动应用程序: python main.py")
        return 1
    else:
        print("❌ 多数测试失败，应用程序可能无法正常启动")
        print("\n建议操作:")
        print("1. 检查项目完整性")
        print("2. 安装缺失的依赖")
        print("3. 查看详细错误信息")
        return 2


if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n主应用程序测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n主应用程序测试异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
