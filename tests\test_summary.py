#!/usr/bin/env python3
"""
测试总结和环境检查脚本
"""

import sys
import os
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def print_header(title):
    """打印标题"""
    print(f"\n{'='*60}")
    print(f"{title:^60}")
    print('='*60)


def print_section(title):
    """打印章节标题"""
    print(f"\n{'-'*40}")
    print(f"{title}")
    print('-'*40)


def check_project_structure():
    """检查项目结构"""
    print_section("项目结构检查")
    
    required_dirs = [
        "config",
        "core", 
        "gui",
        "utils",
        "tests",
        "resources"
    ]
    
    required_files = [
        "main.py",
        "run.py",
        "requirements.txt",
        "README.md",
        "install.py"
    ]
    
    print("必需目录:")
    for dir_name in required_dirs:
        dir_path = project_root / dir_name
        if dir_path.exists():
            print(f"  ✅ {dir_name}/")
        else:
            print(f"  ❌ {dir_name}/ (缺失)")
    
    print("\n必需文件:")
    for file_name in required_files:
        file_path = project_root / file_name
        if file_path.exists():
            print(f"  ✅ {file_name}")
        else:
            print(f"  ❌ {file_name} (缺失)")
    
    # 检查测试文件
    test_pdf = project_root / "ocr测试.pdf"
    if test_pdf.exists():
        file_size = test_pdf.stat().st_size / (1024 * 1024)
        print(f"  ✅ ocr测试.pdf ({file_size:.2f} MB)")
    else:
        print(f"  ❌ ocr测试.pdf (测试文件缺失)")


def check_python_environment():
    """检查Python环境"""
    print_section("Python环境检查")
    
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"项目根目录: {project_root}")
    
    # 检查虚拟环境
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ 运行在虚拟环境中")
    else:
        print("⚠️  未使用虚拟环境")


def check_basic_imports():
    """检查基本模块导入"""
    print_section("基本模块导入检查")
    
    modules_to_test = [
        ("config.settings", "AppSettings"),
        ("config.models_config", "ModelsConfig"),
        ("utils.file_utils", "FileUtils"),
        ("utils.image_utils", "ImageUtils"),
        ("utils.system_monitor", "SystemMonitor"),
        ("core.ocr_engine", "OCREngine"),
        ("webui.web_controller", "WebController"),
    ]
    
    success_count = 0
    
    for module_name, class_name in modules_to_test:
        try:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            print(f"  ✅ {module_name}.{class_name}")
            success_count += 1
        except ImportError as e:
            print(f"  ❌ {module_name}.{class_name} - 导入失败: {e}")
        except AttributeError as e:
            print(f"  ❌ {module_name}.{class_name} - 类不存在: {e}")
        except Exception as e:
            print(f"  ❌ {module_name}.{class_name} - 其他错误: {e}")
    
    print(f"\n导入成功率: {success_count}/{len(modules_to_test)} ({success_count/len(modules_to_test)*100:.1f}%)")
    return success_count == len(modules_to_test)


def check_dependencies_status():
    """检查依赖状态"""
    print_section("依赖包状态")
    
    # 基础依赖
    basic_deps = [
        ("numpy", "NumPy"),
        ("PIL", "Pillow"),
        ("cv2", "OpenCV"),
        ("pdf2image", "pdf2image"),
        ("psutil", "psutil"),
        ("toml", "TOML"),
        ("tqdm", "tqdm"),
    ]
    
    # 高级依赖
    advanced_deps = [
        ("webview", "pywebview"),
        ("paddle", "PaddlePaddle"),
        ("paddleocr", "PaddleOCR"),
        ("pynvml", "pynvml"),
        ("openpyxl", "openpyxl"),
        ("docx", "python-docx"),
        ("reportlab", "ReportLab"),
    ]
    
    print("基础依赖:")
    basic_available = 0
    for module, name in basic_deps:
        try:
            __import__(module)
            print(f"  ✅ {name}")
            basic_available += 1
        except ImportError:
            print(f"  ❌ {name}")
    
    print(f"\n高级依赖:")
    advanced_available = 0
    for module, name in advanced_deps:
        try:
            __import__(module)
            print(f"  ✅ {name}")
            advanced_available += 1
        except ImportError:
            print(f"  ⚠️  {name}")
    
    print(f"\n依赖状态总结:")
    print(f"  基础依赖: {basic_available}/{len(basic_deps)} ({basic_available/len(basic_deps)*100:.1f}%)")
    print(f"  高级依赖: {advanced_available}/{len(advanced_deps)} ({advanced_available/len(advanced_deps)*100:.1f}%)")
    
    return basic_available, advanced_available, len(basic_deps), len(advanced_deps)


def provide_recommendations(basic_available, basic_total, advanced_available, advanced_total, imports_ok):
    """提供建议"""
    print_section("使用建议")
    
    if imports_ok and basic_available == basic_total:
        print("🎉 环境配置完整，可以运行所有功能！")
        print("\n推荐操作:")
        print("1. 运行完整测试: python tests/run_all_tests.py")
        print("2. 启动应用程序: python main.py")
        print("3. 处理PDF文件: python tests/process_pdf.py")
        
    elif imports_ok and basic_available >= basic_total * 0.8:
        print("✅ 基本环境配置正常，可以运行核心功能")
        print("\n推荐操作:")
        print("1. 运行基础测试: python tests/test_simple.py")
        print("2. 安装缺失依赖: pip install -r requirements.txt")
        print("3. 运行完整测试: python tests/run_all_tests.py")
        
    elif imports_ok:
        print("⚠️  项目结构正常，但缺少重要依赖")
        print("\n推荐操作:")
        print("1. 安装基础依赖: pip install numpy pillow opencv-python pdf2image")
        print("2. 运行依赖检查: python tests/test_dependencies.py")
        print("3. 安装完整依赖: pip install -r requirements.txt")
        
    else:
        print("❌ 项目配置存在问题")
        print("\n推荐操作:")
        print("1. 检查项目完整性")
        print("2. 重新运行安装脚本: python install.py")
        print("3. 检查Python路径和模块导入")
    
    # 特殊提醒
    print(f"\n特殊提醒:")
    if basic_available < basic_total:
        missing_basic = basic_total - basic_available
        print(f"- 缺少 {missing_basic} 个基础依赖，可能影响核心功能")
    
    if "pdf2image" not in [m for m, _ in [("numpy", "NumPy"), ("PIL", "Pillow"), ("cv2", "OpenCV"), ("pdf2image", "pdf2image")] if m in sys.modules]:
        print("- PDF处理需要安装poppler工具 (Windows用户可能需要额外配置)")
    
    if advanced_available < 2:
        print("- OCR功能需要安装PaddleOCR相关包")
        print("- GUI功能需要安装pywebview")


def main():
    """主函数"""
    print_header("Prisma OCR & Translator - 环境检查总结")
    
    # 检查项目结构
    check_project_structure()
    
    # 检查Python环境
    check_python_environment()
    
    # 检查基本导入
    imports_ok = check_basic_imports()
    
    # 检查依赖状态
    basic_available, advanced_available, basic_total, advanced_total = check_dependencies_status()
    
    # 提供建议
    provide_recommendations(basic_available, basic_total, advanced_available, advanced_total, imports_ok)
    
    print_header("检查完成")
    
    # 返回状态码
    if imports_ok and basic_available == basic_total:
        return 0  # 完美
    elif imports_ok and basic_available >= basic_total * 0.8:
        return 1  # 良好
    else:
        return 2  # 需要改进


if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n检查被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n检查过程发生未知错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
