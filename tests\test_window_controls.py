#!/usr/bin/env python3
"""
测试自定义标题栏和窗口控制功能
"""

import sys
import os
from pathlib import Path
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def test_window_controls():
    """测试窗口控制功能"""
    print("测试自定义标题栏和窗口控制功能...")
    
    try:
        from webui.api_bridge import APIBridge
        from webui.web_controller import WebController
        
        # 创建API桥接实例
        api_bridge = APIBridge()
        print("✓ API桥接创建成功")
        
        # 测试窗口控制方法
        print("\n测试窗口控制方法:")
        
        # 测试最小化
        result = api_bridge.minimize_window()
        print(f"最小化窗口: {result}")
        
        # 测试最大化
        result = api_bridge.maximize_window()
        print(f"最大化窗口: {result}")
        
        # 测试关闭（注意：这会关闭窗口）
        # result = api_bridge.close_window()
        # print(f"关闭窗口: {result}")
        
        print("✓ 窗口控制方法测试完成")
        
        # 清理
        api_bridge.cleanup()
        
        return True
        
    except Exception as e:
        print(f"✗ 窗口控制测试失败: {e}")
        return False


def test_frameless_window():
    """测试无边框窗口创建"""
    print("\n测试无边框窗口创建...")
    
    try:
        from webui.web_controller import WebController
        
        # 创建Web控制器
        controller = WebController(debug=False)
        print("✓ Web控制器创建成功")
        
        # 检查HTML文件是否存在
        if controller.html_path and os.path.exists(controller.html_path):
            print(f"✓ HTML文件存在: {controller.html_path}")
        else:
            print(f"✗ HTML文件不存在: {controller.html_path}")
            return False
        
        print("✓ 无边框窗口配置验证完成")
        
        return True
        
    except Exception as e:
        print(f"✗ 无边框窗口测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("Prisma OCR - 自定义标题栏测试")
    print("=" * 50)
    
    tests = [
        ("窗口控制功能", test_window_controls),
        ("无边框窗口", test_frameless_window),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n[{passed + 1}/{total}] {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！自定义标题栏功能准备就绪。")
        print("\n使用说明:")
        print("1. 运行 'python main.py' 启动应用")
        print("2. 应用将显示无系统标题栏的窗口")
        print("3. 使用自定义标题栏的按钮控制窗口")
        print("4. 拖拽标题栏可以移动窗口")
        return True
    else:
        print("❌ 部分测试失败，请检查相关问题。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
