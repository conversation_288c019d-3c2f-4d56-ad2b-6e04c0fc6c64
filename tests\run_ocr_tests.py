#!/usr/bin/env python3
"""
OCR功能测试套件运行器
运行所有OCR相关的测试并生成综合报告
"""

import sys
import os
import time
import subprocess
from pathlib import Path
from typing import Dict, Any, List, Tuple

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class OCRTestSuite:
    """OCR测试套件"""
    
    def __init__(self):
        self.test_results = []
        self.start_time = None
        self.end_time = None
        
        # 定义测试脚本
        self.test_scripts = [
            # 基础测试
            {
                'script': 'test_ocr_engine_core.py',
                'name': 'OCR引擎核心功能',
                'description': '测试OCR引擎的初始化、配置和基本功能',
                'category': '核心功能',
                'priority': 1,
                'timeout': 300
            },
            {
                'script': 'test_image_processing.py',
                'name': '图像处理功能',
                'description': '测试图像加载、预处理、格式转换等功能',
                'category': '图像处理',
                'priority': 2,
                'timeout': 180
            },
            {
                'script': 'test_pdf_processing.py',
                'name': 'PDF处理功能',
                'description': '测试PDF转图像、多页处理等功能',
                'category': 'PDF处理',
                'priority': 2,
                'timeout': 240
            },
            
            # 综合测试
            {
                'script': 'test_comprehensive_ocr.py',
                'name': 'OCR综合功能',
                'description': '使用真实测试文件进行端到端的OCR功能测试',
                'category': '综合测试',
                'priority': 3,
                'timeout': 600
            },
            
            # 错误处理测试
            {
                'script': 'test_error_handling.py',
                'name': '错误处理和异常',
                'description': '测试各种异常情况下的系统行为',
                'category': '错误处理',
                'priority': 4,
                'timeout': 180
            },
            
            # 性能测试
            {
                'script': 'test_performance.py',
                'name': '性能和基准',
                'description': '测试OCR处理的性能指标和资源使用情况',
                'category': '性能测试',
                'priority': 5,
                'timeout': 900
            }
        ]
    
    def check_test_environment(self) -> bool:
        """检查测试环境"""
        print("检查测试环境...")
        
        # 检查测试文件
        test_files = {
            'ocr测试.PNG': project_root / 'ocr测试.PNG',
            'ocr测试.pdf': project_root / 'ocr测试.pdf'
        }
        
        missing_files = []
        for name, path in test_files.items():
            if path.exists():
                file_size = path.stat().st_size / 1024  # KB
                print(f"✅ {name}: {file_size:.1f} KB")
            else:
                print(f"❌ {name}: 文件不存在")
                missing_files.append(name)
        
        if missing_files:
            print(f"\n⚠️  缺少测试文件: {', '.join(missing_files)}")
            print("部分测试可能会跳过或失败")
        
        # 检查Python版本
        python_version = sys.version_info
        print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        if python_version < (3, 8):
            print("⚠️  建议使用Python 3.8或更高版本")
        
        # 检查基本依赖
        basic_deps = ['numpy', 'PIL', 'cv2', 'pathlib']
        missing_deps = []
        
        for dep in basic_deps:
            try:
                __import__(dep)
                print(f"✅ {dep}")
            except ImportError:
                print(f"❌ {dep}")
                missing_deps.append(dep)
        
        if missing_deps:
            print(f"\n❌ 缺少基本依赖: {', '.join(missing_deps)}")
            return False
        
        return True
    
    def run_single_test(self, test_info: Dict[str, Any]) -> Dict[str, Any]:
        """运行单个测试"""
        script_name = test_info['script']
        test_name = test_info['name']
        timeout = test_info.get('timeout', 300)
        
        print(f"\n{'='*60}")
        print(f"运行测试: {test_name}")
        print(f"脚本: {script_name}")
        print(f"描述: {test_info['description']}")
        print(f"类别: {test_info['category']}")
        print('='*60)
        
        script_path = project_root / 'tests' / script_name
        
        if not script_path.exists():
            return {
                'name': test_name,
                'script': script_name,
                'success': False,
                'duration': 0.0,
                'error': f'测试脚本不存在: {script_path}',
                'output': '',
                'category': test_info['category']
            }
        
        start_time = time.time()
        
        try:
            # 运行测试脚本
            result = subprocess.run([
                sys.executable, str(script_path)
            ], 
            capture_output=True, 
            text=True, 
            timeout=timeout,
            cwd=project_root
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            success = result.returncode == 0
            
            # 输出测试结果
            if result.stdout:
                print(result.stdout)
            
            if result.stderr and not success:
                print("错误输出:")
                print(result.stderr)
            
            status = "✅ 通过" if success else "❌ 失败"
            print(f"\n{status} {test_name} (耗时: {duration:.2f}s)")
            
            return {
                'name': test_name,
                'script': script_name,
                'success': success,
                'duration': duration,
                'error': result.stderr if not success else None,
                'output': result.stdout,
                'category': test_info['category'],
                'return_code': result.returncode
            }
            
        except subprocess.TimeoutExpired:
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"\n⏰ 测试超时: {test_name} (超时: {timeout}s)")
            
            return {
                'name': test_name,
                'script': script_name,
                'success': False,
                'duration': duration,
                'error': f'测试超时 ({timeout}s)',
                'output': '',
                'category': test_info['category'],
                'return_code': -1
            }
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"\n❌ 测试异常: {test_name} - {e}")
            
            return {
                'name': test_name,
                'script': script_name,
                'success': False,
                'duration': duration,
                'error': str(e),
                'output': '',
                'category': test_info['category'],
                'return_code': -2
            }
    
    def run_all_tests(self, selected_categories: List[str] = None) -> None:
        """运行所有测试"""
        print("Prisma OCR功能测试套件")
        print("=" * 60)
        print(f"项目根目录: {project_root}")
        print(f"Python版本: {sys.version}")
        
        # 检查测试环境
        if not self.check_test_environment():
            print("\n❌ 测试环境检查失败，部分测试可能无法正常运行")
            response = input("是否继续运行测试？ [y/N]: ").strip().lower()
            if response not in ['y', 'yes', '是']:
                print("测试已取消")
                return
        
        # 过滤测试脚本
        if selected_categories:
            filtered_scripts = [
                script for script in self.test_scripts 
                if script['category'] in selected_categories
            ]
        else:
            filtered_scripts = self.test_scripts
        
        print(f"\n计划运行 {len(filtered_scripts)} 个测试")
        
        # 按优先级排序
        filtered_scripts.sort(key=lambda x: x['priority'])
        
        # 显示测试计划
        print("\n测试计划:")
        for i, test_info in enumerate(filtered_scripts, 1):
            print(f"{i:2d}. {test_info['name']} ({test_info['category']})")
        
        # 确认运行
        try:
            response = input("\n开始运行测试？ [Y/n]: ").strip().lower()
            if response in ['n', 'no', '否']:
                print("测试已取消")
                return
        except KeyboardInterrupt:
            print("\n测试已取消")
            return
        
        # 开始测试
        self.start_time = time.time()
        
        for i, test_info in enumerate(filtered_scripts, 1):
            print(f"\n进度: {i}/{len(filtered_scripts)}")
            
            try:
                result = self.run_single_test(test_info)
                self.test_results.append(result)
                
            except KeyboardInterrupt:
                print(f"\n\n用户中断测试")
                break
        
        self.end_time = time.time()
        
        # 生成报告
        self.generate_report()
    
    def generate_report(self) -> None:
        """生成测试报告"""
        if not self.test_results:
            print("没有测试结果")
            return
        
        total_duration = self.end_time - self.start_time if self.end_time and self.start_time else 0
        
        # 统计结果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r['success'])
        failed_tests = total_tests - passed_tests
        
        # 按类别统计
        category_stats = {}
        for result in self.test_results:
            category = result['category']
            if category not in category_stats:
                category_stats[category] = {'total': 0, 'passed': 0}
            
            category_stats[category]['total'] += 1
            if result['success']:
                category_stats[category]['passed'] += 1
        
        # 生成报告
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("Prisma OCR功能测试报告")
        report_lines.append("=" * 80)
        report_lines.append(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"总耗时: {total_duration:.2f}秒")
        report_lines.append(f"总测试数: {total_tests}")
        report_lines.append(f"通过: {passed_tests}")
        report_lines.append(f"失败: {failed_tests}")
        report_lines.append(f"成功率: {(passed_tests/total_tests*100):.1f}%")
        report_lines.append("")
        
        # 按类别统计
        report_lines.append("按类别统计:")
        report_lines.append("-" * 40)
        for category, stats in category_stats.items():
            success_rate = (stats['passed'] / stats['total'] * 100) if stats['total'] > 0 else 0
            report_lines.append(f"{category}: {stats['passed']}/{stats['total']} ({success_rate:.1f}%)")
        report_lines.append("")
        
        # 详细结果
        report_lines.append("详细测试结果:")
        report_lines.append("-" * 40)
        
        for result in self.test_results:
            status = "✅ 通过" if result['success'] else "❌ 失败"
            duration = result['duration']
            name = result['name']
            category = result['category']
            
            report_lines.append(f"{status} {name} ({category}) - {duration:.2f}s")
            
            if not result['success'] and result['error']:
                error_lines = result['error'].split('\n')
                for line in error_lines[:3]:  # 只显示前3行错误
                    if line.strip():
                        report_lines.append(f"    错误: {line.strip()}")
        
        # 性能统计
        if any('performance' in r.get('output', '').lower() for r in self.test_results):
            report_lines.append("")
            report_lines.append("性能统计:")
            report_lines.append("-" * 40)
            
            for result in self.test_results:
                if result['category'] == '性能测试' and result['success']:
                    report_lines.append(f"{result['name']}: {result['duration']:.2f}s")
        
        # 建议和总结
        report_lines.append("")
        report_lines.append("建议和总结:")
        report_lines.append("-" * 40)
        
        if failed_tests == 0:
            report_lines.append("🎉 所有测试通过！OCR功能运行正常。")
            report_lines.append("系统已准备好进行生产使用。")
        elif passed_tests > failed_tests:
            report_lines.append("⚠️  大部分测试通过，但仍有问题需要解决。")
            report_lines.append("建议:")
            report_lines.append("1. 检查失败测试的错误信息")
            report_lines.append("2. 确认所有依赖包已正确安装")
            report_lines.append("3. 检查测试文件是否存在且完整")
        else:
            report_lines.append("❌ 多数测试失败，系统可能存在严重问题。")
            report_lines.append("建议:")
            report_lines.append("1. 检查Python环境和依赖包安装")
            report_lines.append("2. 运行 'pip install -r requirements.txt'")
            report_lines.append("3. 确认测试文件存在")
            report_lines.append("4. 检查系统资源（内存、磁盘空间）")
        
        # 输出报告
        report_content = "\n".join(report_lines)
        print("\n" + report_content)
        
        # 保存报告
        report_path = project_root / "tests" / "OCR_COMPREHENSIVE_TEST_REPORT.md"
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            print(f"\n📄 测试报告已保存: {report_path}")
        except Exception as e:
            print(f"\n⚠️  保存报告失败: {e}")


def main():
    """主函数"""
    suite = OCRTestSuite()
    
    # 解析命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == '--help' or sys.argv[1] == '-h':
            print("OCR功能测试套件")
            print("用法: python run_ocr_tests.py [选项]")
            print("")
            print("选项:")
            print("  --help, -h     显示帮助信息")
            print("  --core         只运行核心功能测试")
            print("  --basic        运行基础测试（核心+图像+PDF）")
            print("  --full         运行完整测试（默认）")
            print("  --performance  只运行性能测试")
            return 0
        
        elif sys.argv[1] == '--core':
            selected_categories = ['核心功能']
        elif sys.argv[1] == '--basic':
            selected_categories = ['核心功能', '图像处理', 'PDF处理']
        elif sys.argv[1] == '--performance':
            selected_categories = ['性能测试']
        else:
            selected_categories = None
    else:
        selected_categories = None
    
    try:
        suite.run_all_tests(selected_categories)
        
        # 返回适当的退出码
        if suite.test_results:
            failed_count = sum(1 for r in suite.test_results if not r['success'])
            return 1 if failed_count > 0 else 0
        else:
            return 1
            
    except KeyboardInterrupt:
        print("\n\n测试套件被用户中断")
        return 1
    except Exception as e:
        print(f"\n测试套件运行异常: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
