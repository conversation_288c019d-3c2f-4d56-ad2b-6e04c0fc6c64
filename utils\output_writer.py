"""
输出文件写入工具
处理OCR结果的文件输出功能
"""

import logging
import time
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
from datetime import datetime

from core.ocr_engine import OCRResult
from utils.file_utils import FileUtils


class OutputWriter:
    """输出文件写入器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def write_results(self, results: List[OCRResult], 
                     input_path: str,
                     output_format: str,
                     output_dir: Optional[str] = None,
                     merge_tables: bool = False) -> Dict[str, Any]:
        """
        写入OCR结果到文件
        
        Args:
            results: OCR结果列表
            input_path: 输入文件路径
            output_format: 输出格式 ('txt', 'xlsx', 'docx', 'pdf')
            output_dir: 输出目录（可选）
            merge_tables: 是否合并表格
            
        Returns:
            写入结果信息
        """
        try:
            # 生成输出文件路径
            output_path = FileUtils.get_output_filename(
                input_path, output_format, output_dir
            )
            
            # 确保输出目录存在
            FileUtils.ensure_directory(output_path.parent)
            
            # 根据格式选择写入方法
            if output_format.lower() == 'txt':
                return self._write_txt(results, output_path, input_path)
            elif output_format.lower() == 'xlsx':
                return self._write_xlsx(results, output_path, input_path, merge_tables)
            elif output_format.lower() == 'docx':
                return self._write_docx(results, output_path, input_path)
            elif output_format.lower() == 'pdf':
                return self._write_pdf(results, output_path, input_path)
            else:
                return {
                    'success': False,
                    'error': f'不支持的输出格式: {output_format}'
                }
                
        except Exception as e:
            self.logger.error(f"写入文件失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _write_txt(self, results: List[OCRResult], output_path: Path, input_path: str) -> Dict[str, Any]:
        """写入TXT格式"""
        try:
            total_text_blocks = 0
            total_chars = 0
            
            with open(output_path, 'w', encoding='utf-8') as f:
                # 写入文件头
                f.write("Prisma OCR 识别结果\n")
                f.write("=" * 50 + "\n")
                f.write(f"源文件: {Path(input_path).name}\n")
                f.write(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"总页数: {len(results)}\n")
                f.write("=" * 50 + "\n\n")
                
                # 写入每页内容
                for i, result in enumerate(results, 1):
                    if result.text_blocks:
                        f.write(f"=== 第 {i} 页 ===\n")
                        
                        page_text = ""
                        for block in result.text_blocks:
                            text = block.get('text', '').strip()
                            if text:
                                page_text += text + "\n"
                                total_text_blocks += 1
                                total_chars += len(text)
                        
                        f.write(page_text)
                        f.write("\n")
                    else:
                        f.write(f"=== 第 {i} 页 ===\n")
                        f.write("(未识别到文本内容)\n\n")
                
                # 写入统计信息
                f.write("\n" + "=" * 50 + "\n")
                f.write("识别统计:\n")
                f.write(f"总页数: {len(results)}\n")
                f.write(f"文本块数: {total_text_blocks}\n")
                f.write(f"字符数: {total_chars}\n")
                
                # 写入每页详细统计
                for i, result in enumerate(results, 1):
                    blocks_count = len(result.text_blocks)
                    processing_time = getattr(result, 'processing_time', 0)
                    f.write(f"第 {i} 页: {blocks_count} 个文本块, 处理时间: {processing_time:.2f}s\n")
            
            file_size = output_path.stat().st_size
            
            return {
                'success': True,
                'output_path': str(output_path),
                'file_size': file_size,
                'statistics': {
                    'total_pages': len(results),
                    'text_blocks': total_text_blocks,
                    'characters': total_chars
                }
            }
            
        except Exception as e:
            self.logger.error(f"写入TXT文件失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _write_xlsx(self, results: List[OCRResult], output_path: Path, input_path: str, merge_tables: bool = False) -> Dict[str, Any]:
        """写入XLSX格式"""
        try:
            import openpyxl
            from openpyxl.styles import Font, Alignment
            
            # 创建工作簿
            wb = openpyxl.Workbook()
            
            if merge_tables:
                # 合并所有内容到一个工作表
                ws = wb.active
                ws.title = "OCR结果"
                
                # 设置标题
                ws['A1'] = "Prisma OCR 识别结果"
                ws['A1'].font = Font(bold=True, size=14)
                ws['A2'] = f"源文件: {Path(input_path).name}"
                ws['A3'] = f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                ws['A4'] = f"总页数: {len(results)}"
                
                current_row = 6
                total_text_blocks = 0
                
                for i, result in enumerate(results, 1):
                    ws[f'A{current_row}'] = f"第 {i} 页"
                    ws[f'A{current_row}'].font = Font(bold=True)
                    current_row += 1
                    
                    if result.text_blocks:
                        for block in result.text_blocks:
                            text = block.get('text', '').strip()
                            confidence = block.get('confidence', 0)
                            if text:
                                ws[f'A{current_row}'] = text
                                ws[f'B{current_row}'] = f"{confidence:.2f}"
                                current_row += 1
                                total_text_blocks += 1
                    else:
                        ws[f'A{current_row}'] = "(未识别到文本内容)"
                        current_row += 1
                    
                    current_row += 1  # 空行分隔
                
                # 设置列宽
                ws.column_dimensions['A'].width = 80
                ws.column_dimensions['B'].width = 10
                
                # 添加表头
                ws['A5'] = "识别文本"
                ws['B5'] = "置信度"
                ws['A5'].font = Font(bold=True)
                ws['B5'].font = Font(bold=True)
                
            else:
                # 每页一个工作表
                wb.remove(wb.active)  # 删除默认工作表
                total_text_blocks = 0
                
                for i, result in enumerate(results, 1):
                    ws = wb.create_sheet(f"第{i}页")
                    
                    # 设置标题
                    ws['A1'] = f"第 {i} 页 - OCR结果"
                    ws['A1'].font = Font(bold=True, size=12)
                    
                    if result.text_blocks:
                        # 添加表头
                        ws['A3'] = "识别文本"
                        ws['B3'] = "置信度"
                        ws['A3'].font = Font(bold=True)
                        ws['B3'].font = Font(bold=True)
                        
                        row = 4
                        for block in result.text_blocks:
                            text = block.get('text', '').strip()
                            confidence = block.get('confidence', 0)
                            if text:
                                ws[f'A{row}'] = text
                                ws[f'B{row}'] = f"{confidence:.2f}"
                                row += 1
                                total_text_blocks += 1
                    else:
                        ws['A3'] = "(未识别到文本内容)"
                    
                    # 设置列宽
                    ws.column_dimensions['A'].width = 80
                    ws.column_dimensions['B'].width = 10
            
            # 保存文件
            wb.save(output_path)
            
            file_size = output_path.stat().st_size
            
            return {
                'success': True,
                'output_path': str(output_path),
                'file_size': file_size,
                'statistics': {
                    'total_pages': len(results),
                    'text_blocks': total_text_blocks,
                    'worksheets': 1 if merge_tables else len(results)
                }
            }
            
        except ImportError:
            return {
                'success': False,
                'error': 'openpyxl库未安装，无法生成Excel文件'
            }
        except Exception as e:
            self.logger.error(f"写入XLSX文件失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _write_docx(self, results: List[OCRResult], output_path: Path, input_path: str) -> Dict[str, Any]:
        """写入DOCX格式"""
        try:
            from docx import Document
            from docx.shared import Inches
            
            doc = Document()
            
            # 添加标题
            title = doc.add_heading('Prisma OCR 识别结果', 0)
            
            # 添加文档信息
            doc.add_paragraph(f"源文件: {Path(input_path).name}")
            doc.add_paragraph(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            doc.add_paragraph(f"总页数: {len(results)}")
            doc.add_paragraph()  # 空行
            
            total_text_blocks = 0
            
            # 添加每页内容
            for i, result in enumerate(results, 1):
                # 页面标题
                doc.add_heading(f'第 {i} 页', level=1)
                
                if result.text_blocks:
                    for block in result.text_blocks:
                        text = block.get('text', '').strip()
                        if text:
                            doc.add_paragraph(text)
                            total_text_blocks += 1
                else:
                    doc.add_paragraph("(未识别到文本内容)")
                
                doc.add_paragraph()  # 页面间空行
            
            # 添加统计信息
            doc.add_heading('识别统计', level=1)
            doc.add_paragraph(f"总页数: {len(results)}")
            doc.add_paragraph(f"文本块数: {total_text_blocks}")
            
            # 保存文件
            doc.save(output_path)
            
            file_size = output_path.stat().st_size
            
            return {
                'success': True,
                'output_path': str(output_path),
                'file_size': file_size,
                'statistics': {
                    'total_pages': len(results),
                    'text_blocks': total_text_blocks
                }
            }
            
        except ImportError:
            return {
                'success': False,
                'error': 'python-docx库未安装，无法生成Word文档'
            }
        except Exception as e:
            self.logger.error(f"写入DOCX文件失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _write_pdf(self, results: List[OCRResult], output_path: Path, input_path: str) -> Dict[str, Any]:
        """写入PDF格式"""
        try:
            from reportlab.pdfgen import canvas
            from reportlab.lib.pagesizes import letter, A4
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
            from reportlab.lib.units import inch
            
            # 创建PDF
            c = canvas.Canvas(str(output_path), pagesize=A4)
            width, height = A4
            
            # 设置字体（支持中文）
            try:
                # 尝试使用系统字体
                import platform
                if platform.system() == "Windows":
                    font_path = "C:/Windows/Fonts/simsun.ttc"
                else:
                    font_path = "/System/Library/Fonts/PingFang.ttc"
                
                pdfmetrics.registerFont(TTFont('Chinese', font_path))
                c.setFont('Chinese', 12)
            except:
                # 回退到默认字体
                c.setFont('Helvetica', 12)
            
            # 添加标题页
            c.drawString(100, height - 100, "Prisma OCR 识别结果")
            c.drawString(100, height - 130, f"源文件: {Path(input_path).name}")
            c.drawString(100, height - 160, f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            c.drawString(100, height - 190, f"总页数: {len(results)}")
            
            y_position = height - 250
            total_text_blocks = 0
            
            # 添加每页内容
            for i, result in enumerate(results, 1):
                if y_position < 100:  # 需要新页面
                    c.showPage()
                    c.setFont('Chinese', 12) if 'Chinese' in c.getAvailableFonts() else c.setFont('Helvetica', 12)
                    y_position = height - 50
                
                # 页面标题
                c.drawString(100, y_position, f"第 {i} 页")
                y_position -= 30
                
                if result.text_blocks:
                    for block in result.text_blocks:
                        text = block.get('text', '').strip()
                        if text and y_position > 50:
                            # 处理长文本换行
                            lines = self._wrap_text(text, 70)  # 每行约70个字符
                            for line in lines:
                                if y_position < 50:
                                    c.showPage()
                                    c.setFont('Chinese', 12) if 'Chinese' in c.getAvailableFonts() else c.setFont('Helvetica', 12)
                                    y_position = height - 50
                                
                                c.drawString(120, y_position, line)
                                y_position -= 20
                                total_text_blocks += 1
                else:
                    c.drawString(120, y_position, "(未识别到文本内容)")
                    y_position -= 20
                
                y_position -= 20  # 页面间空行
            
            # 保存PDF
            c.save()
            
            file_size = output_path.stat().st_size
            
            return {
                'success': True,
                'output_path': str(output_path),
                'file_size': file_size,
                'statistics': {
                    'total_pages': len(results),
                    'text_blocks': total_text_blocks
                }
            }
            
        except ImportError:
            return {
                'success': False,
                'error': 'reportlab库未安装，无法生成PDF文件'
            }
        except Exception as e:
            self.logger.error(f"写入PDF文件失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _wrap_text(self, text: str, width: int) -> List[str]:
        """文本换行处理"""
        lines = []
        words = text.split()
        current_line = ""
        
        for word in words:
            if len(current_line + " " + word) <= width:
                current_line += " " + word if current_line else word
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word
        
        if current_line:
            lines.append(current_line)
        
        return lines
