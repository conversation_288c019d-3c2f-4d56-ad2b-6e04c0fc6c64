# PaddleOCR API 迁移指南

## 概述

本文档详细说明了如何从传统的 PaddleOCR API 迁移到新的 PP-StructureV3 API，以及两种API的对比和最佳实践。

## API 对比

### 传统 PaddleOCR API

```python
from paddleocr import PaddleOCR

# 初始化
ocr = PaddleOCR(
    use_angle_cls=True,
    lang='ch',
    show_log=False
)

# 使用
result = ocr.ocr(image_path, cls=True)

# 结果格式
# result[0] = [
#     [[[x1,y1], [x2,y2], [x3,y3], [x4,y4]], ('文本内容', 置信度)],
#     ...
# ]
```

### 新的 PP-StructureV3 API

```python
from paddleocr import PPStructureV3

# 初始化
pipeline = PPStructureV3(
    use_seal_recognition=True,
    use_table_recognition=True,
    use_formula_recognition=True,
    use_chart_recognition=False,
    use_region_detection=True
)

# 使用
results = pipeline.predict(image_path)

# 结果格式
# results[0].json = {
#     'parsing_res_list': [...],
#     'overall_ocr_res': {...},
#     'table_res_list': [...],
#     'formula_res_list': [...],
#     'seal_res_list': [...]
# }
```

## 功能对比

| 功能 | 传统 PaddleOCR | PP-StructureV3 | 说明 |
|------|----------------|----------------|------|
| 基础文字识别 | ✅ | ✅ | 都支持 |
| 表格识别 | ❌ | ✅ | PP-StructureV3新增 |
| 公式识别 | ❌ | ✅ | PP-StructureV3新增 |
| 印章识别 | ❌ | ✅ | PP-StructureV3新增 |
| 版面分析 | ❌ | ✅ | PP-StructureV3新增 |
| 文档预处理 | ❌ | ✅ | PP-StructureV3新增 |
| 多格式输出 | ❌ | ✅ | PP-StructureV3新增 |
| 可视化结果 | ❌ | ✅ | PP-StructureV3新增 |

## 迁移步骤

### 1. 安装要求更新

**旧版本安装：**
```bash
pip install paddleocr
```

**新版本安装：**
```bash
pip install "paddleocr[all]"
```

### 2. 导入方式更新

**旧版本：**
```python
from paddleocr import PaddleOCR
```

**新版本：**
```python
from paddleocr import PPStructureV3
# 或者同时使用两种API
from paddleocr import PaddleOCR, PPStructureV3
```

### 3. 初始化方式更新

**旧版本：**
```python
ocr = PaddleOCR(
    use_angle_cls=True,
    lang='ch',
    det_db_thresh=0.3,
    det_db_box_thresh=0.6,
    rec_batch_num=6
)
```

**新版本：**
```python
pipeline = PPStructureV3(
    # 文本检测参数
    text_det_thresh=0.3,
    text_det_box_thresh=0.6,
    text_rec_batch_size=6,
    
    # 功能开关
    use_seal_recognition=True,
    use_table_recognition=True,
    use_formula_recognition=True,
    
    # 设备选择
    device='cpu'
)
```

### 4. 调用方式更新

**旧版本：**
```python
result = ocr.ocr(image_path, cls=True)
```

**新版本：**
```python
results = pipeline.predict(image_path)
```

### 5. 结果处理更新

**旧版本结果处理：**
```python
if result and result[0]:
    for line in result[0]:
        bbox = line[0]  # 边界框坐标
        text_info = line[1]  # (文本, 置信度)
        text = text_info[0]
        confidence = text_info[1]
        print(f"文本: {text}, 置信度: {confidence}")
```

**新版本结果处理：**
```python
for result in results:
    # 方式1：直接打印
    result.print()
    
    # 方式2：获取JSON数据
    json_data = result.json
    
    # 方式3：保存为不同格式
    result.save_to_json("output.json")
    result.save_to_markdown("output.md")
    result.save_to_html("tables.html")
    
    # 方式4：处理具体内容
    parsing_results = json_data.get('parsing_res_list', [])
    for block in parsing_results:
        block_type = block.get('block_label')
        block_content = block.get('block_content')
        print(f"{block_type}: {block_content}")
```

## 兼容性方案

### 在 Prisma 项目中的兼容实现

项目中的 `core/ocr_engine.py` 已经实现了两种API的兼容：

```python
# 设置传统模式
ocr_engine.set_mode('general')  # 使用传统PaddleOCR

# 设置新模式
ocr_engine.set_mode('structure_v3')  # 使用PP-StructureV3

# 统一的调用接口
result = ocr_engine.process_image(image_path)
```

### 渐进式迁移策略

**阶段1：并行运行**
```python
# 同时使用两种API进行对比
from paddleocr import PaddleOCR, PPStructureV3

# 传统方式
ocr = PaddleOCR(use_angle_cls=True, lang='ch')
traditional_result = ocr.ocr(image_path)

# 新方式
pipeline = PPStructureV3()
new_result = pipeline.predict(image_path)

# 对比结果
compare_results(traditional_result, new_result)
```

**阶段2：功能增强**
```python
# 根据需求选择API
if need_advanced_features:
    # 使用PP-StructureV3获得更多功能
    pipeline = PPStructureV3(
        use_table_recognition=True,
        use_formula_recognition=True
    )
    result = pipeline.predict(image_path)
else:
    # 使用传统API保持简单
    ocr = PaddleOCR(use_angle_cls=True, lang='ch')
    result = ocr.ocr(image_path)
```

**阶段3：完全迁移**
```python
# 完全使用PP-StructureV3
pipeline = PPStructureV3()
results = pipeline.predict(image_path)
```

## 性能对比

| 指标 | 传统 PaddleOCR | PP-StructureV3 | 说明 |
|------|----------------|----------------|------|
| 初始化时间 | 快 | 慢 | PP-StructureV3需要加载更多模型 |
| 内存占用 | 低 | 高 | PP-StructureV3功能更丰富 |
| 处理速度 | 快 | 中等 | 取决于启用的功能数量 |
| 功能丰富度 | 基础 | 丰富 | PP-StructureV3支持更多功能 |
| 结果准确性 | 好 | 更好 | PP-StructureV3有更好的版面分析 |

## 最佳实践

### 1. 选择合适的API

**使用传统 PaddleOCR 的场景：**
- 只需要基础文字识别
- 对性能要求较高
- 内存资源有限
- 简单的文档处理

**使用 PP-StructureV3 的场景：**
- 需要表格识别
- 需要公式识别
- 需要版面分析
- 需要多格式输出
- 复杂文档处理

### 2. 参数优化

**传统 PaddleOCR 优化：**
```python
ocr = PaddleOCR(
    use_angle_cls=True,      # 根据需要开启
    lang='ch',               # 选择合适的语言
    det_db_thresh=0.3,       # 调整检测阈值
    rec_batch_num=6          # 调整批次大小
)
```

**PP-StructureV3 优化：**
```python
pipeline = PPStructureV3(
    # 只启用需要的功能
    use_seal_recognition=False,    # 不需要印章识别时关闭
    use_formula_recognition=False, # 不需要公式识别时关闭
    use_chart_recognition=False,   # 不需要图表解析时关闭
    
    # 性能优化
    enable_hpi=True,              # 启用高性能推理
    cpu_threads=8                 # 调整CPU线程数
)
```

### 3. 错误处理

```python
def robust_ocr_processing(image_path):
    """健壮的OCR处理"""
    
    try:
        # 优先尝试PP-StructureV3
        from paddleocr import PPStructureV3
        pipeline = PPStructureV3()
        results = pipeline.predict(image_path)
        return results, 'structure_v3'
        
    except ImportError:
        # 回退到传统PaddleOCR
        from paddleocr import PaddleOCR
        ocr = PaddleOCR(use_angle_cls=True, lang='ch')
        result = ocr.ocr(image_path)
        return result, 'traditional'
        
    except Exception as e:
        print(f"OCR处理失败: {e}")
        return None, 'error'
```

## 常见问题

### Q1: 如何判断是否支持PP-StructureV3？

```python
try:
    from paddleocr import PPStructureV3
    print("✅ 支持PP-StructureV3")
except ImportError:
    print("❌ 不支持PP-StructureV3，请安装完整版")
```

### Q2: 两种API的结果如何转换？

```python
def convert_traditional_to_structure_format(traditional_result):
    """将传统结果转换为结构化格式"""
    
    converted = {
        'parsing_res_list': [],
        'overall_ocr_res': {
            'rec_texts': [],
            'rec_scores': [],
            'rec_polys': []
        }
    }
    
    if traditional_result and traditional_result[0]:
        for line in traditional_result[0]:
            bbox = line[0]
            text, confidence = line[1]
            
            converted['overall_ocr_res']['rec_texts'].append(text)
            converted['overall_ocr_res']['rec_scores'].append(confidence)
            converted['overall_ocr_res']['rec_polys'].append(bbox)
    
    return converted
```

### Q3: 如何在现有项目中平滑迁移？

1. **保持向后兼容**：同时支持两种API
2. **渐进式替换**：逐步替换功能模块
3. **充分测试**：确保迁移后功能正常
4. **性能监控**：监控迁移后的性能变化

## 总结

PP-StructureV3 提供了更强大的文档分析能力，但也带来了更高的资源消耗。在迁移时应该：

1. **评估需求**：确定是否需要高级功能
2. **测试性能**：在目标环境中测试性能表现
3. **渐进迁移**：采用渐进式迁移策略
4. **保持兼容**：在过渡期保持API兼容性

通过合理的迁移策略，可以充分利用PP-StructureV3的强大功能，同时保持系统的稳定性和性能。
