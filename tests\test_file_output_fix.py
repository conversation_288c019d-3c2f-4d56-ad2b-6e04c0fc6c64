#!/usr/bin/env python3
"""
测试文件输出修复
验证OCR处理后是否真正创建文件
"""

import sys
import os
import time
import shutil
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def test_output_writer():
    """测试输出写入器"""
    print("测试输出写入器...")
    
    try:
        from utils.output_writer import OutputWriter
        from core.ocr_engine import OCRResult
        
        # 创建模拟OCR结果
        results = []
        for i in range(2):  # 模拟2页
            result = OCRResult()
            result.page_number = i + 1
            result.add_text_block(f"这是第{i+1}页的测试文本", [0, 0, 100, 20], 0.95)
            result.add_text_block(f"第{i+1}页的第二行文本", [0, 25, 100, 45], 0.90)
            results.append(result)
        
        # 创建临时输入文件
        temp_dir = Path(tempfile.mkdtemp(prefix="output_test_"))
        input_file = temp_dir / "test_input.png"
        input_file.touch()  # 创建空文件
        
        writer = OutputWriter()
        
        # 测试TXT输出
        print("  测试TXT输出...")
        txt_result = writer.write_results(
            results=results,
            input_path=str(input_file),
            output_format='txt'
        )
        
        if txt_result.get('success'):
            output_path = Path(txt_result['output_path'])
            if output_path.exists():
                print(f"  [PASS] TXT文件创建成功: {output_path}")
                print(f"  文件大小: {txt_result['file_size']} bytes")
                print(f"  统计信息: {txt_result['statistics']}")
            else:
                print(f"  [FAIL] TXT文件未创建: {output_path}")
                return False
        else:
            print(f"  [FAIL] TXT写入失败: {txt_result.get('error')}")
            return False
        
        # 测试XLSX输出
        print("  测试XLSX输出...")
        xlsx_result = writer.write_results(
            results=results,
            input_path=str(input_file),
            output_format='xlsx'
        )
        
        if xlsx_result.get('success'):
            output_path = Path(xlsx_result['output_path'])
            if output_path.exists():
                print(f"  [PASS] XLSX文件创建成功: {output_path}")
                print(f"  文件大小: {xlsx_result['file_size']} bytes")
                print(f"  统计信息: {xlsx_result['statistics']}")
            else:
                print(f"  [FAIL] XLSX文件未创建: {output_path}")
                return False
        else:
            print(f"  [FAIL] XLSX写入失败: {xlsx_result.get('error')}")
            return False
        
        # 清理临时文件
        shutil.rmtree(temp_dir)
        
        return True
        
    except Exception as e:
        print(f"[FAIL] 输出写入器测试失败: {e}")
        return False


def test_api_bridge_processing():
    """测试API桥接的真实处理"""
    print("\n测试API桥接的真实处理...")
    
    try:
        from webui.api_bridge import APIBridge
        from webui.web_controller import WebController
        
        # 创建Web控制器和API桥接
        web_controller = WebController(debug=True)
        api_bridge = APIBridge()
        api_bridge.set_window_controller(web_controller)
        
        # 检查新增的方法
        methods_to_check = [
            'show_completion_dialog',
            'open_output_file',
            'open_output_folder'
        ]
        
        for method_name in methods_to_check:
            if hasattr(api_bridge, method_name):
                print(f"  [PASS] {method_name} 方法存在")
            else:
                print(f"  [FAIL] {method_name} 方法不存在")
                return False
        
        return True
        
    except Exception as e:
        print(f"[FAIL] API桥接测试失败: {e}")
        return False


def test_real_ocr_processing():
    """测试真实OCR处理（如果测试文件存在）"""
    print("\n测试真实OCR处理...")
    
    # 检查测试文件
    test_image = project_root / "ocr测试.png"
    test_pdf = project_root / "ocr测试.pdf"
    
    test_file = None
    if test_image.exists():
        test_file = test_image
        print(f"  使用测试图像: {test_file}")
    elif test_pdf.exists():
        test_file = test_pdf
        print(f"  使用测试PDF: {test_file}")
    else:
        print("  [SKIP] 测试文件不存在，跳过真实OCR测试")
        return True
    
    try:
        from core.ocr_engine import OCREngine
        from utils.image_utils import ImageUtils
        from utils.output_writer import OutputWriter
        from utils.file_utils import FileUtils
        
        # 初始化OCR引擎
        print("  初始化OCR引擎...")
        from config.settings import AppSettings
        from config.models_config import ModelsConfig

        settings = AppSettings()
        models_config = ModelsConfig()
        ocr_engine = OCREngine(settings.ocr, models_config)
        if not ocr_engine.initialize():
            print("  [FAIL] OCR引擎初始化失败")
            return False
        
        # 加载文件
        print("  加载文件...")
        if FileUtils.is_pdf_file(test_file):
            images = ImageUtils.convert_pdf_to_images(test_file, first_page=1, last_page=1)
        else:
            images = [ImageUtils.load_image_pil(test_file)]
        
        print(f"  加载完成，共{len(images)}页")
        
        # OCR识别
        print("  进行OCR识别...")
        results = []
        for i, image in enumerate(images):
            result = ocr_engine.process_image(image, page_number=i+1)
            results.append(result)
            print(f"  第{i+1}页识别完成，文本块数: {len(result.text_blocks)}")
        
        # 保存结果
        print("  保存结果...")
        temp_dir = Path(tempfile.mkdtemp(prefix="real_ocr_test_"))
        
        writer = OutputWriter()
        
        # 测试TXT输出
        txt_result = writer.write_results(
            results=results,
            input_path=str(test_file),
            output_format='txt',
            output_dir=str(temp_dir)
        )
        
        if txt_result.get('success'):
            output_path = Path(txt_result['output_path'])
            print(f"  [PASS] 真实OCR结果保存成功: {output_path}")
            print(f"  文件大小: {txt_result['file_size']} bytes")
            print(f"  统计信息: {txt_result['statistics']}")
            
            # 显示部分内容
            with open(output_path, 'r', encoding='utf-8') as f:
                content = f.read()[:200]
                print(f"  内容预览: {content}...")
            
            # 保留文件供用户查看
            final_path = project_root / f"ocr_test_result_{int(time.time())}.txt"
            shutil.copy2(output_path, final_path)
            print(f"  结果文件已保存到: {final_path}")
        else:
            print(f"  [FAIL] 真实OCR结果保存失败: {txt_result.get('error')}")
            return False
        
        # 清理临时目录
        shutil.rmtree(temp_dir)
        
        return True
        
    except Exception as e:
        print(f"[FAIL] 真实OCR处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_completion_dialog_info():
    """测试完成对话框信息生成"""
    print("\n测试完成对话框信息生成...")
    
    try:
        from webui.api_bridge import APIBridge
        from webui.web_controller import WebController

        # 创建Web控制器和API桥接
        web_controller = WebController(debug=True)
        api_bridge = APIBridge()
        api_bridge.set_window_controller(web_controller)
        
        # 模拟输出信息
        output_info = {
            'output_path': '/path/to/output/file.txt',
            'file_size': 1024 * 1024,  # 1MB
            'statistics': {
                'total_pages': 3,
                'text_blocks': 25,
                'characters': 1500
            }
        }
        
        result = api_bridge.show_completion_dialog(output_info)
        
        if result.get('success'):
            completion_info = result.get('completion_info')
            print(f"  [PASS] 完成信息生成成功")
            print(f"  输出路径: {completion_info['output_path']}")
            print(f"  文件大小: {completion_info['file_size']}")
            print(f"  统计信息: {completion_info['statistics']}")
            return True
        else:
            print(f"  [FAIL] 完成信息生成失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"[FAIL] 完成对话框信息测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("文件输出修复验证测试")
    print("=" * 50)
    
    test_results = []
    
    # 1. 测试输出写入器
    result = test_output_writer()
    test_results.append(("输出写入器", result))
    
    # 2. 测试API桥接处理
    result = test_api_bridge_processing()
    test_results.append(("API桥接处理", result))
    
    # 3. 测试真实OCR处理
    result = test_real_ocr_processing()
    test_results.append(("真实OCR处理", result))
    
    # 4. 测试完成对话框信息
    result = test_completion_dialog_info()
    test_results.append(("完成对话框信息", result))
    
    # 生成报告
    print(f"\n{'='*50}")
    print("测试结果汇总")
    print(f"{'='*50}")
    
    passed = sum(1 for _, success in test_results if success)
    total = len(test_results)
    
    print(f"总测试数: {total}")
    print(f"通过: {passed}")
    print(f"失败: {total - passed}")
    print(f"成功率: {(passed/total*100):.1f}%")
    
    print(f"\n详细结果:")
    for test_name, success in test_results:
        status = "[PASS]" if success else "[FAIL]"
        print(f"  {status} {test_name}")
    
    if passed == total:
        print(f"\n🎉 所有测试通过！文件输出功能已修复！")
        print("\n修复内容:")
        print("- ✅ 添加了完整的输出写入器")
        print("- ✅ 修复了API桥接的真实OCR处理")
        print("- ✅ 实现了完成提示对话框")
        print("- ✅ 添加了打开文件/文件夹功能")
        print("\n现在可以启动应用程序进行实际测试！")
        return 0
    else:
        print(f"\n⚠️  {total-passed} 项测试失败，需要进一步检查")
        return 1


if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程发生未知错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
