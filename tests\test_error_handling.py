#!/usr/bin/env python3
"""
错误处理和异常测试
测试各种异常情况下的系统行为
"""

import sys
import os
import tempfile
from pathlib import Path
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from test_utils import TestUtils, OCRTestRunner


def test_invalid_file_handling() -> bool:
    """测试无效文件处理"""
    print("测试无效文件处理...")
    
    try:
        from utils.file_utils import FileUtils
        from utils.image_utils import ImageUtils
        
        # 测试不存在的文件
        try:
            non_existent_file = Path("non_existent_file.pdf")
            is_supported = FileUtils.is_supported_input_file(non_existent_file)
            print(f"✅ 不存在文件检查: {is_supported}")
            
            # 尝试加载不存在的图像
            try:
                ImageUtils.load_image(non_existent_file)
                print("❌ 应该抛出异常")
                return False
            except (ValueError, FileNotFoundError, Exception):
                print("✅ 正确处理不存在的图像文件")
                
        except Exception as e:
            print(f"❌ 不存在文件测试异常: {e}")
            return False
        
        # 测试空文件
        try:
            with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as temp_file:
                temp_path = Path(temp_file.name)
            
            try:
                file_size = FileUtils.get_file_size_mb(temp_path)
                print(f"✅ 空文件大小检查: {file_size} MB")
                
                if file_size != 0.0:
                    print("❌ 空文件大小应该为0")
                    return False
                    
            finally:
                temp_path.unlink(missing_ok=True)
                
        except Exception as e:
            print(f"❌ 空文件测试异常: {e}")
            return False
        
        # 测试无效格式文件
        try:
            invalid_formats = [
                "test.txt",
                "test.doc",
                "test.exe",
                "test.unknown"
            ]
            
            for invalid_file in invalid_formats:
                is_supported = FileUtils.is_supported_input_file(invalid_file)
                if is_supported:
                    print(f"❌ {invalid_file} 不应该被支持")
                    return False
                else:
                    print(f"✅ 正确拒绝 {invalid_file}")
                    
        except Exception as e:
            print(f"❌ 无效格式测试异常: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 无效文件处理测试异常: {e}")
        return False


def test_corrupted_file_handling() -> bool:
    """测试损坏文件处理"""
    print("测试损坏文件处理...")
    
    try:
        from utils.image_utils import ImageUtils
        
        # 创建损坏的图像文件
        try:
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
                temp_file.write(b"This is not a valid PNG file")
                corrupted_image_path = Path(temp_file.name)
            
            try:
                # 尝试加载损坏的图像
                try:
                    ImageUtils.load_image(corrupted_image_path)
                    print("❌ 应该抛出异常")
                    return False
                except (ValueError, Exception):
                    print("✅ 正确处理损坏的图像文件")
                
                # 尝试用PIL加载损坏的图像
                try:
                    ImageUtils.load_image_pil(corrupted_image_path)
                    print("❌ PIL应该抛出异常")
                    return False
                except Exception:
                    print("✅ PIL正确处理损坏的图像文件")
                    
            finally:
                corrupted_image_path.unlink(missing_ok=True)
                
        except Exception as e:
            print(f"❌ 损坏图像文件测试异常: {e}")
            return False
        
        # 创建损坏的PDF文件
        try:
            with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
                temp_file.write(b"This is not a valid PDF file")
                corrupted_pdf_path = Path(temp_file.name)
            
            try:
                # 尝试转换损坏的PDF
                try:
                    ImageUtils.convert_pdf_to_images(corrupted_pdf_path)
                    print("❌ PDF转换应该抛出异常")
                    return False
                except (ValueError, Exception):
                    print("✅ 正确处理损坏的PDF文件")
                    
            finally:
                corrupted_pdf_path.unlink(missing_ok=True)
                
        except Exception as e:
            print(f"❌ 损坏PDF文件测试异常: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 损坏文件处理测试异常: {e}")
        return False


def test_memory_limit_handling() -> bool:
    """测试内存限制处理"""
    print("测试内存限制处理...")
    
    try:
        import psutil
        from utils.image_utils import ImageUtils
        import numpy as np
        from PIL import Image
        
        # 获取当前内存使用情况
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        print(f"初始内存使用: {initial_memory:.1f} MB")
        
        # 创建一个大图像来测试内存处理
        try:
            # 创建一个相对较大的图像（但不会耗尽内存）
            large_image = Image.new('RGB', (2000, 2000), color='white')
            
            # 测试图像处理
            cv_image = ImageUtils.pil_to_cv2(large_image)
            print(f"✅ 大图像转换成功，尺寸: {cv_image.shape}")
            
            # 测试图像预处理
            enhanced_image = ImageUtils.enhance_image_for_ocr(cv_image)
            print(f"✅ 大图像预处理成功，尺寸: {enhanced_image.shape}")
            
            # 检查内存使用
            current_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = current_memory - initial_memory
            print(f"内存增加: {memory_increase:.1f} MB")
            
            # 清理大对象
            del large_image, cv_image, enhanced_image
            
            # 强制垃圾回收
            import gc
            gc.collect()
            
            # 检查内存释放
            after_cleanup_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_released = current_memory - after_cleanup_memory
            print(f"释放内存: {memory_released:.1f} MB")
            
            return True
            
        except MemoryError:
            print("✅ 正确处理内存不足异常")
            return True
        except Exception as e:
            print(f"❌ 内存限制测试异常: {e}")
            return False
        
    except Exception as e:
        print(f"❌ 内存限制处理测试异常: {e}")
        return False


def test_ocr_engine_error_handling() -> bool:
    """测试OCR引擎错误处理"""
    print("测试OCR引擎错误处理...")
    
    try:
        from core.ocr_engine import OCREngine
        from config.settings import AppSettings
        from config.models_config import ModelsConfig
        
        # 创建OCR引擎
        settings = AppSettings()
        models_config = ModelsConfig()
        ocr_engine = OCREngine(settings.ocr, models_config)
        
        # 测试未初始化状态下的处理
        try:
            from PIL import Image
            test_image = Image.new('RGB', (100, 100), color='white')
            
            # 尝试在未初始化状态下处理图像
            try:
                result = ocr_engine.process_image(test_image)
                print("❌ 应该抛出未初始化异常")
                return False
            except RuntimeError as e:
                if "未初始化" in str(e) or "not initialized" in str(e).lower():
                    print("✅ 正确处理未初始化状态")
                else:
                    print(f"❌ 异常信息不正确: {e}")
                    return False
            except Exception as e:
                print(f"✅ 正确抛出异常: {type(e).__name__}")
                
        except Exception as e:
            print(f"❌ 未初始化测试异常: {e}")
            return False
        
        # 测试无效模式设置
        try:
            invalid_modes = ['invalid', 'unknown', '', None]
            
            for mode in invalid_modes:
                try:
                    ocr_engine.set_mode(mode)
                    print(f"❌ 应该拒绝无效模式: {mode}")
                    return False
                except (ValueError, TypeError):
                    print(f"✅ 正确拒绝无效模式: {mode}")
                except Exception as e:
                    print(f"✅ 正确处理无效模式 {mode}: {type(e).__name__}")
                    
        except Exception as e:
            print(f"❌ 无效模式测试异常: {e}")
            return False
        
        # 测试无效图像输入
        try:
            invalid_inputs = [None, "", "invalid_path", 123, []]
            
            for invalid_input in invalid_inputs:
                try:
                    # 这里不会真正处理，因为引擎未初始化
                    # 但可以测试输入验证
                    if invalid_input is not None:
                        # 只测试非None值，因为None会在其他地方被处理
                        pass
                    print(f"✅ 处理无效输入: {type(invalid_input).__name__}")
                except Exception:
                    print(f"✅ 正确拒绝无效输入: {type(invalid_input).__name__}")
                    
        except Exception as e:
            print(f"❌ 无效输入测试异常: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ OCR引擎错误处理测试异常: {e}")
        return False


def test_configuration_error_handling() -> bool:
    """测试配置错误处理"""
    print("测试配置错误处理...")
    
    try:
        from config.settings import AppSettings
        from config.models_config import ModelsConfig
        
        # 测试配置加载
        try:
            settings = AppSettings()
            print("✅ 应用设置加载成功")
            
            # 验证默认值
            if hasattr(settings, 'ocr'):
                print(f"  - OCR语言: {settings.ocr.lang}")
                print(f"  - 使用GPU: {settings.ocr.use_gpu}")
            else:
                print("❌ OCR设置缺失")
                return False
                
        except Exception as e:
            print(f"❌ 应用设置加载失败: {e}")
            return False
        
        # 测试模型配置
        try:
            models_config = ModelsConfig()
            print("✅ 模型配置加载成功")
            
            # 测试获取不存在的模型路径
            try:
                ocr_paths = models_config.get_ocr_model_paths()
                print(f"✅ OCR模型路径获取: {len(ocr_paths) if ocr_paths else 0} 个路径")
                
                table_paths = models_config.get_table_model_paths()
                print(f"✅ 表格模型路径获取: {len(table_paths) if table_paths else 0} 个路径")
                
            except Exception as e:
                print(f"❌ 模型路径获取异常: {e}")
                return False
            
            # 测试缺失模型检查
            try:
                missing_models = models_config.get_missing_models()
                print(f"✅ 缺失模型检查: {len(missing_models)} 个缺失")
                
                if missing_models:
                    print("  - 这是正常的，首次运行时模型文件可能不存在")
                
            except Exception as e:
                print(f"❌ 缺失模型检查异常: {e}")
                return False
                
        except Exception as e:
            print(f"❌ 模型配置加载失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 配置错误处理测试异常: {e}")
        return False


def test_timeout_handling() -> bool:
    """测试超时处理"""
    print("测试超时处理...")
    
    try:
        # 测试超时上下文管理器
        try:
            import time
            
            # 测试正常情况（不超时）
            try:
                with TestUtils.timeout_context(2):
                    time.sleep(0.5)
                print("✅ 正常操作未超时")
            except TimeoutError:
                print("❌ 正常操作不应该超时")
                return False
            
            # 测试超时情况
            try:
                with TestUtils.timeout_context(1):
                    time.sleep(2)
                print("❌ 应该发生超时")
                return False
            except TimeoutError:
                print("✅ 正确处理超时")
            except Exception as e:
                print(f"✅ 超时处理异常: {type(e).__name__}")
                
        except Exception as e:
            print(f"❌ 超时上下文测试异常: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 超时处理测试异常: {e}")
        return False


def test_resource_cleanup() -> bool:
    """测试资源清理"""
    print("测试资源清理...")
    
    try:
        import psutil
        import gc
        
        # 记录初始内存
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 创建一些对象
        large_objects = []
        
        try:
            # 创建一些大对象
            for i in range(5):
                import numpy as np
                large_array = np.zeros((1000, 1000), dtype=np.float32)
                large_objects.append(large_array)
            
            # 检查内存增加
            after_creation_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = after_creation_memory - initial_memory
            print(f"创建对象后内存增加: {memory_increase:.1f} MB")
            
            # 清理对象
            large_objects.clear()
            del large_objects
            
            # 强制垃圾回收
            gc.collect()
            
            # 检查内存释放
            after_cleanup_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_released = after_creation_memory - after_cleanup_memory
            print(f"清理后释放内存: {memory_released:.1f} MB")
            
            if memory_released > 0:
                print("✅ 资源清理有效")
            else:
                print("⚠️  内存可能未完全释放（这可能是正常的）")
            
            return True
            
        except Exception as e:
            print(f"❌ 资源清理测试异常: {e}")
            return False
        
    except Exception as e:
        print(f"❌ 资源清理测试异常: {e}")
        return False


def main():
    """主测试函数"""
    print("错误处理和异常测试")
    print("=" * 50)
    
    # 创建测试运行器
    runner = OCRTestRunner()
    runner.setup()
    
    try:
        # 定义测试用例
        test_cases = [
            (test_invalid_file_handling, "无效文件处理"),
            (test_corrupted_file_handling, "损坏文件处理"),
            (test_memory_limit_handling, "内存限制处理"),
            (test_ocr_engine_error_handling, "OCR引擎错误处理"),
            (test_configuration_error_handling, "配置错误处理"),
            (test_timeout_handling, "超时处理"),
            (test_resource_cleanup, "资源清理"),
        ]
        
        # 运行测试
        for test_func, test_name in test_cases:
            print(f"\n{'='*20} {test_name} {'='*20}")
            runner.run_test(test_func, test_name)
        
        # 生成报告
        print("\n" + "="*60)
        print(runner.generate_report())
        
        # 保存报告
        runner.save_report("error_handling_test_report.txt")
        
        # 返回结果
        passed = sum(1 for r in runner.results if r['success'])
        total = len(runner.results)
        
        if passed == total:
            print(f"\n🎉 所有测试通过！错误处理功能正常。")
            return 0
        else:
            print(f"\n⚠️  {passed}/{total} 测试通过，请检查失败的测试。")
            return 1
    
    finally:
        runner.cleanup()


if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程发生未知错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
