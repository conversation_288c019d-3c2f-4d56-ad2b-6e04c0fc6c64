"""
文件处理API
处理文件相关的操作
"""

import os
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional

from utils.file_utils import FileUtils


class FileAPI:
    """文件处理API类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.current_files: List[str] = []
        
    def validate_file(self, file_path: str) -> Dict[str, Any]:
        """
        验证文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            验证结果
        """
        try:
            if not file_path:
                return {
                    'valid': False,
                    'error': '文件路径为空'
                }
            
            file_path_obj = Path(file_path)
            
            if not file_path_obj.exists():
                return {
                    'valid': False,
                    'error': '文件不存在'
                }
            
            if not FileUtils.is_supported_input_file(file_path):
                return {
                    'valid': False,
                    'error': f'不支持的文件格式: {file_path_obj.suffix}'
                }
            
            # 检查文件大小（限制为100MB）
            file_size_mb = FileUtils.get_file_size_mb(file_path)
            if file_size_mb > 100:
                return {
                    'valid': False,
                    'error': f'文件过大: {file_size_mb:.1f}MB (最大支持100MB)'
                }
            
            return {
                'valid': True,
                'file_info': {
                    'name': file_path_obj.name,
                    'size_mb': file_size_mb,
                    'type': 'PDF' if FileUtils.is_pdf_file(file_path) else '图像',
                    'extension': file_path_obj.suffix.lower()
                }
            }
            
        except Exception as e:
            self.logger.error(f"验证文件失败: {e}")
            return {
                'valid': False,
                'error': f'验证文件时发生错误: {str(e)}'
            }
    
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件信息
        """
        try:
            validation_result = self.validate_file(file_path)
            
            if not validation_result['valid']:
                return {
                    'success': False,
                    'error': validation_result['error']
                }
            
            file_path_obj = Path(file_path)
            file_info = validation_result['file_info']
            
            # 添加更多文件信息
            file_info.update({
                'path': str(file_path_obj.absolute()),
                'parent_dir': str(file_path_obj.parent),
                'stem': file_path_obj.stem,
                'created_time': os.path.getctime(file_path),
                'modified_time': os.path.getmtime(file_path),
                'size_bytes': FileUtils.get_file_size(file_path)
            })
            
            return {
                'success': True,
                'file_info': file_info
            }
            
        except Exception as e:
            self.logger.error(f"获取文件信息失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def set_current_file(self, file_path: str) -> Dict[str, Any]:
        """
        设置当前文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            操作结果
        """
        try:
            validation_result = self.validate_file(file_path)
            
            if not validation_result['valid']:
                return {
                    'success': False,
                    'error': validation_result['error']
                }
            
            self.current_files = [file_path]
            
            self.logger.info(f"设置当前文件: {file_path}")
            
            return {
                'success': True,
                'file_info': validation_result['file_info']
            }
            
        except Exception as e:
            self.logger.error(f"设置当前文件失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def set_current_files(self, file_paths: List[str]) -> Dict[str, Any]:
        """
        设置当前文件列表
        
        Args:
            file_paths: 文件路径列表
            
        Returns:
            操作结果
        """
        try:
            if not file_paths:
                return {
                    'success': False,
                    'error': '文件列表为空'
                }
            
            valid_files = []
            file_infos = []
            errors = []
            
            for file_path in file_paths:
                validation_result = self.validate_file(file_path)
                
                if validation_result['valid']:
                    valid_files.append(file_path)
                    file_infos.append(validation_result['file_info'])
                else:
                    errors.append(f"{Path(file_path).name}: {validation_result['error']}")
            
            if not valid_files:
                return {
                    'success': False,
                    'error': '没有有效的文件',
                    'errors': errors
                }
            
            self.current_files = valid_files
            
            self.logger.info(f"设置当前文件列表: {len(valid_files)} 个文件")
            
            result = {
                'success': True,
                'file_count': len(valid_files),
                'file_infos': file_infos
            }
            
            if errors:
                result['warnings'] = errors
            
            return result
            
        except Exception as e:
            self.logger.error(f"设置当前文件列表失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_current_files(self) -> List[str]:
        """
        获取当前文件列表
        
        Returns:
            当前文件列表
        """
        return self.current_files.copy()
    
    def clear_current_files(self) -> Dict[str, Any]:
        """
        清空当前文件列表
        
        Returns:
            操作结果
        """
        try:
            self.current_files.clear()
            
            self.logger.info("清空当前文件列表")
            
            return {
                'success': True,
                'message': '已清空文件列表'
            }
            
        except Exception as e:
            self.logger.error(f"清空文件列表失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_supported_formats(self) -> Dict[str, Any]:
        """
        获取支持的文件格式
        
        Returns:
            支持的格式信息
        """
        return {
            'input_formats': {
                'images': list(FileUtils.SUPPORTED_IMAGE_FORMATS),
                'pdf': list(FileUtils.SUPPORTED_PDF_FORMATS)
            },
            'output_formats': list(FileUtils.SUPPORTED_OUTPUT_FORMATS),
            'descriptions': {
                'images': '支持的图像格式',
                'pdf': '支持的PDF格式',
                'output': '支持的输出格式'
            }
        }
    
    def generate_output_path(self, input_path: str, output_format: str, 
                           output_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        生成输出文件路径
        
        Args:
            input_path: 输入文件路径
            output_format: 输出格式
            output_dir: 输出目录（可选）
            
        Returns:
            输出路径信息
        """
        try:
            if not FileUtils.validate_output_format(output_format):
                return {
                    'success': False,
                    'error': f'不支持的输出格式: {output_format}'
                }
            
            output_path = FileUtils.get_output_filename(
                input_path, output_format, output_dir
            )
            
            return {
                'success': True,
                'output_path': str(output_path),
                'output_dir': str(output_path.parent),
                'output_name': output_path.name
            }
            
        except Exception as e:
            self.logger.error(f"生成输出路径失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def create_output_directory(self, output_dir: str) -> Dict[str, Any]:
        """
        创建输出目录
        
        Args:
            output_dir: 输出目录路径
            
        Returns:
            操作结果
        """
        try:
            FileUtils.ensure_directory(output_dir)
            
            self.logger.info(f"创建输出目录: {output_dir}")
            
            return {
                'success': True,
                'message': f'输出目录已创建: {output_dir}'
            }
            
        except Exception as e:
            self.logger.error(f"创建输出目录失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
