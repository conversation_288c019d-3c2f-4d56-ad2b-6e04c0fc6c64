#!/usr/bin/env python3
"""
测试OCR结果解析
"""

from utils.image_utils import ImageUtils
import numpy as np
import cv2

def test_result_parsing():
    print("=== 测试OCR结果解析 ===")
    
    # 测试图像
    test_image = 'ocr测试.PNG'
    
    # 加载图像并转换格式
    print(f"正在加载图像: {test_image}")
    pil_image = ImageUtils.load_image_pil(test_image)
    
    # 确保是RGB格式
    if pil_image.mode != 'RGB':
        pil_image = pil_image.convert('RGB')
    
    img_array = np.array(pil_image)
    print(f"图像形状: {img_array.shape}")
    
    try:
        from paddleocr import PaddleOCR
        
        # 创建OCR实例
        print("正在初始化OCR引擎...")
        ocr = PaddleOCR(lang='ch')
        
        # 执行OCR
        print("正在执行OCR识别...")
        result = ocr.ocr(img_array)
        
        print(f"OCR结果类型: {type(result)}")
        print(f"OCR结果长度: {len(result) if result else 0}")
        
        if result:
            # 检查第一页结果
            page_result = result[0]
            print(f"第一页结果类型: {type(page_result)}")
            print(f"第一页结果长度: {len(page_result) if page_result else 0}")
            
            if page_result:
                print(f"\n✓ 成功识别到 {len(page_result)} 个文本块")
                
                # 解析每个文本块
                for i, block in enumerate(page_result):
                    print(f"\n文本块 {i+1}:")
                    print(f"  类型: {type(block)}")
                    print(f"  长度: {len(block) if hasattr(block, '__len__') else 'N/A'}")
                    
                    if isinstance(block, (list, tuple)) and len(block) >= 2:
                        # 坐标信息
                        coords = block[0]
                        print(f"  坐标: {coords}")
                        
                        # 文本信息
                        text_info = block[1]
                        print(f"  文本信息类型: {type(text_info)}")
                        
                        if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                            text = text_info[0]
                            confidence = text_info[1]
                            print(f"  文本: '{text}'")
                            print(f"  置信度: {confidence:.3f}")
                        else:
                            print(f"  文本信息: {text_info}")
                    else:
                        print(f"  原始数据: {block}")
                
                # 提取所有文本
                print(f"\n=== 提取的所有文本 ===")
                all_texts = []
                for i, block in enumerate(page_result):
                    if isinstance(block, (list, tuple)) and len(block) >= 2:
                        text_info = block[1]
                        if isinstance(text_info, (list, tuple)) and len(text_info) >= 1:
                            text = text_info[0]
                            confidence = text_info[1] if len(text_info) >= 2 else 0.0
                            all_texts.append((text, confidence))
                            print(f"{i+1:2d}. {text} (置信度: {confidence:.3f})")
                
                print(f"\n🎉 成功解析了 {len(all_texts)} 个文本块！")
                
                # 合并所有文本
                combined_text = '\n'.join([text for text, _ in all_texts])
                print(f"\n=== 合并文本 ===")
                print(combined_text)
                
                return all_texts
            else:
                print("✗ 第一页结果为空")
        else:
            print("✗ OCR结果为空")
            
    except Exception as e:
        print(f"✗ OCR处理失败: {e}")
        import traceback
        traceback.print_exc()
    
    return None

if __name__ == "__main__":
    test_result_parsing()
