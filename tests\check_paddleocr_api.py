#!/usr/bin/env python3
"""
检查PaddleOCR API参数
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_paddleocr_api():
    """检查PaddleOCR API"""
    try:
        import paddleocr
        print(f"PaddleOCR版本: {paddleocr.__version__}")
        
        from paddleocr import PaddleOCR
        
        # 尝试不同的参数组合
        print("\n测试基本参数...")
        try:
            ocr = PaddleOCR(lang='ch')
            print("[PASS] 基本参数 lang='ch'")
        except Exception as e:
            print(f"[FAIL] 基本参数失败: {e}")
        
        print("\n测试最小参数...")
        try:
            ocr = PaddleOCR()
            print("[PASS] 无参数初始化")
        except Exception as e:
            print(f"[FAIL] 无参数初始化失败: {e}")
        
        # 检查构造函数签名
        print("\n检查构造函数签名...")
        import inspect
        sig = inspect.signature(PaddleOCR.__init__)
        print("PaddleOCR.__init__ 参数:")
        for param_name, param in sig.parameters.items():
            if param_name != 'self':
                default = param.default if param.default != inspect.Parameter.empty else "无默认值"
                print(f"  {param_name}: {default}")
        
    except Exception as e:
        print(f"检查PaddleOCR API失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_paddleocr_api()
