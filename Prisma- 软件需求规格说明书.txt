# Prisma-OCR&translator - 软件需求规格说明书

- **文档版本:** 1.0
- **最后更新日期:** 2025年9月10日


---

## 1. 项目名称
本地PDF与图像OCR及翻译工具

## 2. 功能需求 (Functional Requirements)

#### FR1: 文件输入
- 程序应允许用户通过图形界面（GUI）选择本地文件作为输入。
- 支持的输入文件类型包括：
    - PDF 文件 (`.pdf`)
    - 常见的图像文件格式，如 `.png`, `.jpg`, `.jpeg`, `.bmp`, `.tiff` 等所有PaddleOCR支持的格式。

#### FR2: 处理模式与选项
- **FR2.1: 基础模式**
  用户必须在以下两种基础模式中选择一种，这决定了程序的主要任务目标：
    - **通用模式**: 识别并提取输入文件中的所有文本内容和基础版面结构。适用于常规文档、文章、报告等。
    - **表格模式**: 专注于识别和提取文件中的表格数据，并进行结构化输出。适用于包含清晰表格的文档。

- **FR2.2: 翻译选项**
    - 程序需提供一个可勾选的“启用翻译”选项。
    - 此选项对“通用模式”和“表格模式”均可适用。
    - 若勾选此选项，程序将在识别出文本后，调用 `PP-DocTranslation` 产线将其翻译成指定语言，并尽最大努力在输出时保持原始的版面结构（包括文本布局和表格结构）。

#### FR3: 输出配置
- **FR3.1: 输出格式**
    - **通用模式 (不翻译)**: 支持导出为 `.txt`, `.docx`, `.pdf` (双层可搜索), `.xlsx` 等格式。
    - **表格模式 (不翻译)**: 仅支持导出为 `.xlsx` 格式。
    - **启用翻译时**: 输出格式将力求与不翻译时保持一致。例如，“表格模式 + 翻译”应输出一个包含翻译后文本的 `.xlsx` 文件。

- **FR3.2: 输出路径**
    - 用户可通过界面自定义输出文件的保存路径和文件名。
    - 如果用户未指定，输出文件将默认保存在与输入文件相同的目录中。

- **FR3.3: 表格合并选项**
    - 在“表格模式”下，提供一个布尔选项（复选框），允许用户将一个多页PDF中所有识别出的表格合并到最终生成的单个Excel文件的同一个Sheet中。
    - 此选项在启用翻译时同样有效。

#### FR4: 进度与状态反馈
- 程序GUI界面必须实时显示当前任务的处理进度。
- 进度展示应清晰易懂，包含当前执行步骤的文字描述以及量化进度（例如，“2/5: 正在识别第1页”）。
- 任务处理完成后，无论成功或失败，都应在界面上给予用户明确的弹窗或状态提示。

#### FR5: 系统资源监控
- GUI界面需要包含一个面板或区域，用于实时显示当前计算机的系统资源使用情况。
- 必须展示的指标包括：
    - CPU 使用率 (%)
    - 内存 使用率 (%)
    - GPU 使用率 (%) (如果系统中存在并被程序使用)

## 3. 非功能需求 (Non-functional Requirements)

- **NFR1: 响应式界面**
    - 由于OCR和模型加载是计算密集型任务，程序必须采用多线程架构，确保在后台处理任务时，用户交互的GUI界面不会出现冻结、卡顿或“未响应”现象。

- **NFR2: 本地化运行**
    - 本程序是一个完全独立的桌面应用程序。所有的数据处理、模型计算均在用户本地计算机上完成。
    - 除首次下载或更新模型外，程序核心功能不依赖任何网络连接或外部云服务。

- **NFR3: 平台兼容性**
    - 后端核心逻辑应具备跨平台能力，能在主流的桌面操作系统（Windows, macOS, Linux）上正确运行。

## 4. 参考资料与网址

- **PaddleOCR 总项目地址**:
    - **网址**: `https://github.com/PaddlePaddle/PaddleOCR`
    - **内容**: PaddleOCR项目的官方GitHub仓库，包含完整的源代码、预训练模型库、安装指南和项目文档。

- **PP-StructureV3 产线文档**:
    - **网址**: `https://www.paddleocr.ai/latest/version3.x/pipeline_usage/PP-StructureV3.html`
    - **内容**: PP-StructureV3流水线的详细技术文档，介绍了其集成的版面分析、表格识别、公式识别等功能。这是本工具“通用模式”和“表格模式”的技术基础。

- **通用OCR 子产线参考**:
    - **网址**: `https://www.paddleocr.ai/latest/version3.x/pipeline_usage/OCR.html#1-ocr`
    - **内容**: 描述了标准的OCR流程，包括文本行方向分类、文本检测和文本识别模块。

- **表格识别 子产线参考**:
    - **网址**: `https://www.paddleocr.ai/latest/version3.x/pipeline_usage/table_recognition_v2.html`
    - **内容**: 详细说明了表格识别v2模型的结构、使用方法和参数配置，是“表格模式”的核心技术参考。

- **PP-DocTranslation 产线文档**:
    - **网址**: `https://www.paddleocr.ai/latest/version3.x/pipeline_usage/PP-DocTranslation.html`
    - **内容**: 描述了文档翻译流水线的功能和用法，该流水线可以端到端地完成对文档图像内容的识别和翻译。这是本工具“翻译选项”功能的技术基础。

## 5. 技术栈 (后端)

- **核心框架**: PaddleOCR, paddlepaddle-gpu (或 paddlepaddle)
- **并发模型**: Python `threading` 和 `queue` 模块
- **PDF处理**: pdf2image, Poppler
- **系统监控**: psutil, pynvml (用于NVIDIA GPU)
- **文件操作**:
    - Excel: openpyxl / pandas
    - Word: python-docx