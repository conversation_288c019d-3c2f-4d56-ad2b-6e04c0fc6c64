#!/usr/bin/env python3
"""
OCR综合功能测试
使用真实测试文件进行端到端的OCR功能测试
"""

import sys
import os
import time
from pathlib import Path
from typing import Dict, Any, Optional, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from test_utils import TestUtils, OCRTestRunner


def test_end_to_end_image_ocr() -> bool:
    """测试端到端图像OCR处理"""
    print("测试端到端图像OCR...")
    
    try:
        from core.ocr_engine import OCREngine
        from config.settings import AppSettings
        from config.models_config import ModelsConfig
        from utils.image_utils import ImageUtils
        
        # 获取测试文件
        test_files = TestUtils.get_test_files()
        
        if 'png' not in test_files:
            print("⚠️  图像测试文件不存在，跳过图像OCR测试")
            return True
        
        image_path = test_files['png']
        print(f"处理图像文件: {image_path.name}")
        
        # 创建OCR引擎
        settings = AppSettings()
        models_config = ModelsConfig()
        ocr_engine = OCREngine(settings.ocr, models_config)
        
        # 设置回调函数
        progress_updates = []
        error_messages = []
        
        def progress_callback(progress: int, status: str):
            progress_updates.append((progress, status))
            print(f"  进度: {progress}% - {status}")
        
        def error_callback(error_msg: str):
            error_messages.append(error_msg)
            print(f"  错误: {error_msg}")
        
        ocr_engine.progress_callback = progress_callback
        ocr_engine.error_callback = error_callback
        
        # 初始化OCR引擎
        print("初始化OCR引擎...")
        start_time = time.time()
        
        try:
            with TestUtils.timeout_context(180):  # 3分钟超时
                init_success = ocr_engine.initialize()
            
            if not init_success:
                print("❌ OCR引擎初始化失败")
                return False
            
            init_time = time.time() - start_time
            print(f"✅ OCR引擎初始化成功 (耗时: {init_time:.1f}s)")
            
        except TimeoutError:
            print("❌ OCR引擎初始化超时")
            return False
        
        # 加载和预处理图像
        try:
            print("加载图像...")
            pil_image = ImageUtils.load_image_pil(image_path)
            print(f"✅ 图像加载成功，尺寸: {pil_image.size}")
            
            # 处理图像
            print("开始OCR识别...")
            ocr_start_time = time.time()
            
            result = ocr_engine.process_image(pil_image, page_number=1)
            
            ocr_time = time.time() - ocr_start_time
            print(f"✅ OCR处理完成 (耗时: {ocr_time:.2f}s)")
            
            # 验证结果
            validation = TestUtils.validate_ocr_result(result)
            
            if validation['is_valid']:
                print(f"✅ OCR结果验证通过")
                print(f"  - 识别文本块数: {validation['text_count']}")
                print(f"  - 平均置信度: {validation['average_confidence']:.2f}")
                
                # 显示部分识别结果
                if hasattr(result, 'text_blocks') and result.text_blocks:
                    print("  - 识别文本示例:")
                    for i, block in enumerate(result.text_blocks[:3]):
                        text = block.get('text', '').strip()
                        confidence = block.get('confidence', 0.0)
                        if text:
                            print(f"    {i+1}. {text[:50]}... (置信度: {confidence:.2f})")
                
                return True
            else:
                print(f"❌ OCR结果验证失败: {validation['errors']}")
                return False
            
        except Exception as e:
            print(f"❌ 图像OCR处理异常: {e}")
            return False
        
    except Exception as e:
        print(f"❌ 端到端图像OCR测试异常: {e}")
        return False


def test_end_to_end_pdf_ocr() -> bool:
    """测试端到端PDF OCR处理"""
    print("测试端到端PDF OCR...")
    
    try:
        from core.ocr_engine import OCREngine
        from config.settings import AppSettings
        from config.models_config import ModelsConfig
        from utils.image_utils import ImageUtils
        
        # 获取测试文件
        test_files = TestUtils.get_test_files()
        
        if 'pdf' not in test_files:
            print("⚠️  PDF测试文件不存在，跳过PDF OCR测试")
            return True
        
        pdf_path = test_files['pdf']
        print(f"处理PDF文件: {pdf_path.name}")
        
        # 创建OCR引擎
        settings = AppSettings()
        models_config = ModelsConfig()
        ocr_engine = OCREngine(settings.ocr, models_config)
        
        # 设置回调函数
        progress_updates = []
        
        def progress_callback(progress: int, status: str):
            progress_updates.append((progress, status))
            print(f"  进度: {progress}% - {status}")
        
        ocr_engine.progress_callback = progress_callback
        
        # 初始化OCR引擎（如果尚未初始化）
        if not ocr_engine.is_initialized:
            print("初始化OCR引擎...")
            try:
                with TestUtils.timeout_context(180):
                    init_success = ocr_engine.initialize()
                
                if not init_success:
                    print("❌ OCR引擎初始化失败")
                    return False
                    
            except TimeoutError:
                print("❌ OCR引擎初始化超时")
                return False
        
        # 转换PDF为图像
        try:
            print("转换PDF为图像...")
            pdf_start_time = time.time()
            
            images = ImageUtils.convert_pdf_to_images(pdf_path, dpi=150, first_page=1, last_page=2)
            
            pdf_time = time.time() - pdf_start_time
            print(f"✅ PDF转换完成，共 {len(images)} 页 (耗时: {pdf_time:.2f}s)")
            
            if len(images) == 0:
                print("❌ PDF转换结果为空")
                return False
            
        except Exception as e:
            print(f"❌ PDF转换失败: {e}")
            return False
        
        # 处理每一页
        all_results = []
        total_text_blocks = 0
        
        for page_num, image in enumerate(images, 1):
            try:
                print(f"处理第 {page_num} 页...")
                page_start_time = time.time()
                
                result = ocr_engine.process_image(image, page_number=page_num)
                
                page_time = time.time() - page_start_time
                print(f"✅ 第 {page_num} 页处理完成 (耗时: {page_time:.2f}s)")
                
                # 验证结果
                validation = TestUtils.validate_ocr_result(result)
                
                if validation['is_valid']:
                    all_results.append(result)
                    total_text_blocks += validation['text_count']
                    print(f"  - 识别文本块数: {validation['text_count']}")
                    print(f"  - 平均置信度: {validation['average_confidence']:.2f}")
                else:
                    print(f"  ⚠️  第 {page_num} 页结果验证失败")
                
            except Exception as e:
                print(f"❌ 第 {page_num} 页处理异常: {e}")
                continue
        
        # 汇总结果
        if all_results:
            print(f"✅ PDF OCR处理完成")
            print(f"  - 成功处理页数: {len(all_results)}")
            print(f"  - 总文本块数: {total_text_blocks}")
            
            # 计算平均置信度
            total_confidence = 0
            confidence_count = 0
            
            for result in all_results:
                if hasattr(result, 'text_blocks'):
                    for block in result.text_blocks:
                        confidence = block.get('confidence', 0.0)
                        if confidence > 0:
                            total_confidence += confidence
                            confidence_count += 1
            
            if confidence_count > 0:
                avg_confidence = total_confidence / confidence_count
                print(f"  - 整体平均置信度: {avg_confidence:.2f}")
            
            return True
        else:
            print("❌ 没有成功处理任何页面")
            return False
        
    except Exception as e:
        print(f"❌ 端到端PDF OCR测试异常: {e}")
        return False


def test_mode_switching_with_real_data() -> bool:
    """测试使用真实数据的模式切换"""
    print("测试模式切换功能...")
    
    try:
        from core.ocr_engine import OCREngine
        from config.settings import AppSettings
        from config.models_config import ModelsConfig
        from utils.image_utils import ImageUtils
        
        # 获取测试文件
        test_files = TestUtils.get_test_files()
        
        if 'png' not in test_files:
            print("⚠️  图像测试文件不存在，跳过模式切换测试")
            return True
        
        image_path = test_files['png']
        
        # 创建OCR引擎
        settings = AppSettings()
        models_config = ModelsConfig()
        ocr_engine = OCREngine(settings.ocr, models_config)
        
        # 初始化OCR引擎
        if not ocr_engine.is_initialized:
            try:
                with TestUtils.timeout_context(180):
                    init_success = ocr_engine.initialize()
                
                if not init_success:
                    print("❌ OCR引擎初始化失败")
                    return False
                    
            except TimeoutError:
                print("❌ OCR引擎初始化超时")
                return False
        
        # 加载图像
        pil_image = ImageUtils.load_image_pil(image_path)
        
        # 测试通用模式
        print("测试通用模式...")
        ocr_engine.set_mode('general')
        
        try:
            general_result = ocr_engine.process_image(pil_image, page_number=1)
            general_validation = TestUtils.validate_ocr_result(general_result)
            
            if general_validation['is_valid']:
                print(f"✅ 通用模式处理成功")
                print(f"  - 文本块数: {general_validation['text_count']}")
                print(f"  - 平均置信度: {general_validation['average_confidence']:.2f}")
            else:
                print("❌ 通用模式处理失败")
                return False
                
        except Exception as e:
            print(f"❌ 通用模式处理异常: {e}")
            return False
        
        # 测试表格模式
        print("测试表格模式...")
        ocr_engine.set_mode('table')
        
        try:
            table_result = ocr_engine.process_image(pil_image, page_number=1)
            table_validation = TestUtils.validate_ocr_result(table_result)
            
            if table_validation['is_valid']:
                print(f"✅ 表格模式处理成功")
                print(f"  - 文本块数: {table_validation['text_count']}")
                print(f"  - 平均置信度: {table_validation['average_confidence']:.2f}")
                
                # 检查是否有表格数据
                if hasattr(table_result, 'tables') and table_result.tables:
                    print(f"  - 识别表格数: {len(table_result.tables)}")
                
            else:
                print("❌ 表格模式处理失败")
                return False
                
        except Exception as e:
            print(f"❌ 表格模式处理异常: {e}")
            return False
        
        # 比较两种模式的结果
        print("比较模式结果...")
        
        general_count = general_validation['text_count']
        table_count = table_validation['text_count']
        
        print(f"  - 通用模式文本块: {general_count}")
        print(f"  - 表格模式文本块: {table_count}")
        
        if general_count > 0 or table_count > 0:
            print("✅ 至少一种模式产生了有效结果")
            return True
        else:
            print("❌ 两种模式都没有产生有效结果")
            return False
        
    except Exception as e:
        print(f"❌ 模式切换测试异常: {e}")
        return False


def test_performance_benchmark() -> bool:
    """测试性能基准"""
    print("测试性能基准...")
    
    try:
        from core.ocr_engine import OCREngine
        from config.settings import AppSettings
        from config.models_config import ModelsConfig
        from utils.image_utils import ImageUtils
        import psutil
        
        # 获取测试文件
        test_files = TestUtils.get_test_files()
        
        if not test_files:
            print("⚠️  没有测试文件，跳过性能测试")
            return True
        
        # 创建OCR引擎
        settings = AppSettings()
        models_config = ModelsConfig()
        ocr_engine = OCREngine(settings.ocr, models_config)
        
        # 初始化OCR引擎
        if not ocr_engine.is_initialized:
            init_start = time.time()
            try:
                with TestUtils.timeout_context(180):
                    init_success = ocr_engine.initialize()
                
                if not init_success:
                    print("❌ OCR引擎初始化失败")
                    return False
                    
            except TimeoutError:
                print("❌ OCR引擎初始化超时")
                return False
            
            init_time = time.time() - init_start
            print(f"✅ 初始化耗时: {init_time:.2f}s")
        
        # 性能测试结果
        performance_results = []
        
        # 测试图像处理性能
        if 'png' in test_files:
            image_path = test_files['png']
            pil_image = ImageUtils.load_image_pil(image_path)
            
            # 多次测试取平均值
            test_runs = 3
            total_time = 0
            
            for run in range(test_runs):
                start_time = time.time()
                result = ocr_engine.process_image(pil_image, page_number=1)
                end_time = time.time()
                
                run_time = end_time - start_time
                total_time += run_time
                
                print(f"  图像处理第 {run+1} 次: {run_time:.2f}s")
            
            avg_time = total_time / test_runs
            performance_results.append(('图像OCR', avg_time))
            print(f"✅ 图像OCR平均耗时: {avg_time:.2f}s")
        
        # 测试PDF处理性能
        if 'pdf' in test_files:
            pdf_path = test_files['pdf']
            
            # PDF转换性能
            pdf_start = time.time()
            images = ImageUtils.convert_pdf_to_images(pdf_path, dpi=150, first_page=1, last_page=1)
            pdf_time = time.time() - pdf_start
            
            performance_results.append(('PDF转换', pdf_time))
            print(f"✅ PDF转换耗时: {pdf_time:.2f}s")
            
            if images:
                # PDF OCR性能
                ocr_start = time.time()
                result = ocr_engine.process_image(images[0], page_number=1)
                ocr_time = time.time() - ocr_start
                
                performance_results.append(('PDF OCR', ocr_time))
                print(f"✅ PDF OCR耗时: {ocr_time:.2f}s")
        
        # 内存使用情况
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        print(f"✅ 当前内存使用: {memory_mb:.1f} MB")
        
        # 性能总结
        if performance_results:
            print("性能基准总结:")
            for task, duration in performance_results:
                print(f"  - {task}: {duration:.2f}s")
            
            # 检查性能是否合理
            slow_tasks = [task for task, duration in performance_results if duration > 30]
            if slow_tasks:
                print(f"⚠️  以下任务耗时较长: {', '.join(slow_tasks)}")
            else:
                print("✅ 所有任务性能表现良好")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能基准测试异常: {e}")
        return False


def main():
    """主测试函数"""
    print("OCR综合功能测试")
    print("=" * 50)
    
    # 创建测试运行器
    runner = OCRTestRunner()
    runner.setup()
    
    try:
        # 定义测试用例
        test_cases = [
            (test_end_to_end_image_ocr, "端到端图像OCR"),
            (test_end_to_end_pdf_ocr, "端到端PDF OCR"),
            (test_mode_switching_with_real_data, "模式切换功能"),
            (test_performance_benchmark, "性能基准测试"),
        ]
        
        # 运行测试
        for test_func, test_name in test_cases:
            print(f"\n{'='*20} {test_name} {'='*20}")
            runner.run_test(test_func, test_name)
        
        # 生成报告
        print("\n" + "="*60)
        print(runner.generate_report())
        
        # 保存报告
        runner.save_report("comprehensive_ocr_test_report.txt")
        
        # 返回结果
        passed = sum(1 for r in runner.results if r['success'])
        total = len(runner.results)
        
        if passed == total:
            print(f"\n🎉 所有测试通过！OCR综合功能正常。")
            return 0
        else:
            print(f"\n⚠️  {passed}/{total} 测试通过，请检查失败的测试。")
            return 1
    
    finally:
        runner.cleanup()


if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程发生未知错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
