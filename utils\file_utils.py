"""
文件处理工具函数
"""

import os
import shutil
from pathlib import Path
from typing import List, Optional, Tuple, Union
import mimetypes


class FileUtils:
    """文件处理工具类"""
    
    # 支持的图像格式
    SUPPORTED_IMAGE_FORMATS = {
        '.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.tif', 
        '.webp', '.gif', '.ico', '.ppm', '.pgm', '.pbm'
    }
    
    # 支持的PDF格式
    SUPPORTED_PDF_FORMATS = {'.pdf'}
    
    # 支持的输出格式
    SUPPORTED_OUTPUT_FORMATS = {
        '.txt', '.docx', '.pdf', '.xlsx'
    }
    
    @classmethod
    def is_supported_input_file(cls, file_path: Union[str, Path]) -> bool:
        """
        检查文件是否为支持的输入格式
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否支持
        """
        file_path = Path(file_path)
        extension = file_path.suffix.lower()
        return extension in cls.SUPPORTED_IMAGE_FORMATS or extension in cls.SUPPORTED_PDF_FORMATS
    
    @classmethod
    def is_image_file(cls, file_path: Union[str, Path]) -> bool:
        """
        检查文件是否为图像文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否为图像文件
        """
        file_path = Path(file_path)
        extension = file_path.suffix.lower()
        return extension in cls.SUPPORTED_IMAGE_FORMATS
    
    @classmethod
    def is_pdf_file(cls, file_path: Union[str, Path]) -> bool:
        """
        检查文件是否为PDF文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否为PDF文件
        """
        file_path = Path(file_path)
        extension = file_path.suffix.lower()
        return extension in cls.SUPPORTED_PDF_FORMATS
    
    @classmethod
    def get_file_size(cls, file_path: Union[str, Path]) -> int:
        """
        获取文件大小（字节）
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件大小
        """
        return Path(file_path).stat().st_size
    
    @classmethod
    def get_file_size_mb(cls, file_path: Union[str, Path]) -> float:
        """
        获取文件大小（MB）
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件大小（MB）
        """
        return cls.get_file_size(file_path) / (1024 * 1024)
    
    @classmethod
    def ensure_directory(cls, directory: Union[str, Path]) -> None:
        """
        确保目录存在，如果不存在则创建
        
        Args:
            directory: 目录路径
        """
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    @classmethod
    def get_unique_filename(cls, file_path: Union[str, Path]) -> Path:
        """
        获取唯一的文件名（如果文件已存在，则添加数字后缀）
        
        Args:
            file_path: 原始文件路径
            
        Returns:
            唯一的文件路径
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            return file_path
        
        base_name = file_path.stem
        extension = file_path.suffix
        directory = file_path.parent
        
        counter = 1
        while True:
            new_name = f"{base_name}_{counter}{extension}"
            new_path = directory / new_name
            if not new_path.exists():
                return new_path
            counter += 1
    
    @classmethod
    def copy_file(cls, src: Union[str, Path], dst: Union[str, Path]) -> None:
        """
        复制文件
        
        Args:
            src: 源文件路径
            dst: 目标文件路径
        """
        src_path = Path(src)
        dst_path = Path(dst)
        
        # 确保目标目录存在
        cls.ensure_directory(dst_path.parent)
        
        shutil.copy2(src_path, dst_path)
    
    @classmethod
    def move_file(cls, src: Union[str, Path], dst: Union[str, Path]) -> None:
        """
        移动文件
        
        Args:
            src: 源文件路径
            dst: 目标文件路径
        """
        src_path = Path(src)
        dst_path = Path(dst)
        
        # 确保目标目录存在
        cls.ensure_directory(dst_path.parent)
        
        shutil.move(str(src_path), str(dst_path))
    
    @classmethod
    def delete_file(cls, file_path: Union[str, Path]) -> None:
        """
        删除文件
        
        Args:
            file_path: 文件路径
        """
        file_path = Path(file_path)
        if file_path.exists():
            file_path.unlink()
    
    @classmethod
    def get_temp_directory(cls) -> Path:
        """
        获取临时目录
        
        Returns:
            临时目录路径
        """
        import tempfile
        temp_dir = Path(tempfile.gettempdir()) / "prisma_ocr"
        cls.ensure_directory(temp_dir)
        return temp_dir
    
    @classmethod
    def clean_temp_directory(cls) -> None:
        """清理临时目录"""
        temp_dir = cls.get_temp_directory()
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
    
    @classmethod
    def get_output_filename(cls, input_path: Union[str, Path], 
                          output_format: str, 
                          output_dir: Optional[Union[str, Path]] = None) -> Path:
        """
        生成输出文件名
        
        Args:
            input_path: 输入文件路径
            output_format: 输出格式（如 'txt', 'docx'）
            output_dir: 输出目录（可选）
            
        Returns:
            输出文件路径
        """
        input_path = Path(input_path)
        
        # 确定输出目录
        if output_dir:
            output_directory = Path(output_dir)
        else:
            output_directory = input_path.parent
        
        # 生成输出文件名
        base_name = input_path.stem
        if not output_format.startswith('.'):
            output_format = f'.{output_format}'
        
        output_filename = f"{base_name}_ocr{output_format}"
        output_path = output_directory / output_filename
        
        # 确保文件名唯一
        return cls.get_unique_filename(output_path)
    
    @classmethod
    def validate_output_format(cls, format_str: str) -> bool:
        """
        验证输出格式是否支持
        
        Args:
            format_str: 格式字符串
            
        Returns:
            是否支持
        """
        if not format_str.startswith('.'):
            format_str = f'.{format_str}'
        return format_str.lower() in cls.SUPPORTED_OUTPUT_FORMATS
    
    @classmethod
    def get_mime_type(cls, file_path: Union[str, Path]) -> Optional[str]:
        """
        获取文件的MIME类型
        
        Args:
            file_path: 文件路径
            
        Returns:
            MIME类型
        """
        mime_type, _ = mimetypes.guess_type(str(file_path))
        return mime_type
