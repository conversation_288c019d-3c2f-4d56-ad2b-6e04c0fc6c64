# PP-DocTranslation 使用指南

## 概述

PP-DocTranslation 是 PaddleOCR 最新推出的文档翻译产线，它基于 PP-StructureV3 提供端到端的文档识别和翻译功能。本文档详细介绍如何在 Prisma OCR & Translator 项目中使用 PP-DocTranslation。

## 功能特性

### 核心功能
- **文档解析**：基于 PP-StructureV3 的强大文档结构化分析
- **版面区域检测**：自动识别文档中的文本、表格、图片、公式等区域
- **文本识别**：高精度的文字识别，支持多语言
- **表格识别**：结构化表格识别，输出HTML格式
- **公式识别**：数学公式识别和LaTeX输出
- **印章识别**：印章文本检测和识别
- **文档翻译**：集成大语言模型的文档翻译功能

### 高级特性
- **阅读顺序分析**：智能分析文档的阅读顺序
- **多格式输出**：支持JSON、Markdown、HTML、Excel等多种输出格式
- **批量处理**：支持PDF文件和图像批量处理
- **可视化结果**：提供详细的可视化分析结果
- **端到端翻译**：从文档识别到翻译的完整流程

## 安装要求

### 基础要求
- Python 3.8+
- PaddlePaddle 3.0.0+
- PaddleOCR 全功能版本

### 安装命令
```bash
# 安装完整功能版本
python -m pip install "paddleocr[all]"
```

## Python脚本方式集成

### 1. 基础导入和初始化

```python
from paddleocr import PPDocTranslation

# 初始化PP-DocTranslation产线
pipeline = PPDocTranslation(
    # 文档预处理选项
    use_doc_orientation_classify=False,  # 文档方向分类
    use_doc_unwarping=False,            # 文档扭曲矫正
    use_textline_orientation=True,      # 文本行方向分类
    
    # 功能模块选项
    use_seal_recognition=True,          # 印章识别
    use_table_recognition=True,         # 表格识别
    use_formula_recognition=True,       # 公式识别
    use_chart_recognition=False,        # 图表解析
    use_region_detection=True,          # 版面区域检测
    
    # 设备选择
    device='cpu'  # 或 'gpu:0'
)
```

### 2. 基础使用示例

```python
# 文档路径
input_path = "document.pdf"  # 支持PDF和图像文件

# 输出目录
output_path = "./output"

# 执行视觉预测
visual_results = pipeline.visual_predict(input_path)

# 处理结果
ori_md_info_list = []
for result in visual_results:
    layout_parsing_result = result["layout_parsing_result"]
    ori_md_info_list.append(layout_parsing_result.markdown)
    
    # 保存为不同格式
    layout_parsing_result.save_to_json(f"{output_path}/result.json")
    layout_parsing_result.save_to_markdown(f"{output_path}/document.md")
    layout_parsing_result.save_to_html(f"{output_path}/tables.html")
    layout_parsing_result.save_to_xlsx(f"{output_path}/tables.xlsx")
    layout_parsing_result.save_to_img(f"{output_path}/visualization/")
```

### 3. 项目配置说明

**重要说明**：在 Prisma OCR & Translator 项目中，PP-DocTranslation 仅用于文档识别和解析功能，**不使用翻译功能**。

```python
# 在项目中，chat_bot_config 永远设置为 None
# 这样只使用 PP-DocTranslation 的视觉识别功能，不进行翻译

# 执行视觉预测（仅识别，不翻译）
visual_results = pipeline.visual_predict(input_path)

# 处理识别结果
for result in visual_results:
    layout_parsing_result = result["layout_parsing_result"]

    # 保存识别结果（不包含翻译）
    layout_parsing_result.save_to_json(f"{output_path}/result.json")
    layout_parsing_result.save_to_markdown(f"{output_path}/document.md")
    layout_parsing_result.save_to_html(f"{output_path}/tables.html")
    layout_parsing_result.save_to_xlsx(f"{output_path}/tables.xlsx")
```

**注意**：项目设计决策是专注于文档识别和解析，翻译功能由项目的其他模块处理。

## 在 Prisma 项目中的集成

### 1. OCR引擎集成

在 `core/ocr_engine.py` 中已经集成了PP-DocTranslation支持：

```python
# 设置为PP-DocTranslation模式
ocr_engine.set_mode('doc_translation')

# 处理图像
result = ocr_engine.process_image(image_path)
```

### 2. 配置选项

在 `config/settings.py` 中的配置：

```python
@dataclass
class OCRSettings:
    # PP-DocTranslation配置
    use_doc_translation: bool = False
    doc_translation_config: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.doc_translation_config is None:
            self.doc_translation_config = {
                'use_doc_orientation_classify': False,
                'use_doc_unwarping': False,
                'use_textline_orientation': True,
                'use_seal_recognition': True,
                'use_table_recognition': True,
                'use_formula_recognition': True,
                'use_chart_recognition': False,
                'use_region_detection': True,
                'device': 'cpu'
            }
```

### 3. GUI集成

在GUI中可以添加PP-DocTranslation的选项：

```python
# 添加模式选择
mode_options = ['general', 'table', 'structure_v3', 'doc_translation']

# 根据选择的模式初始化OCR引擎
if selected_mode == 'doc_translation':
    ocr_engine.set_mode('doc_translation')
```

## 性能优化建议

### 1. 硬件优化
- **GPU加速**：使用GPU可显著提升处理速度
- **内存管理**：确保有足够内存处理大型文档
- **并行处理**：利用多核CPU进行并行处理

### 2. 参数调优
- **检测阈值**：根据文档质量调整检测阈值
- **批次大小**：根据内存情况调整批次大小
- **模型选择**：根据精度和速度需求选择合适的模型

### 3. 使用建议
- **预处理**：对低质量图像进行预处理
- **分页处理**：对大型PDF文件进行分页处理
- **结果缓存**：缓存处理结果避免重复计算

## 常见问题解决

### 1. 安装问题
```bash
# 如果遇到依赖问题，尝试重新安装
pip uninstall paddleocr
pip install "paddleocr[all]"
```

### 2. 内存不足
```python
# 减少批次大小
pipeline = PPDocTranslation(
    text_recognition_batch_size=1,  # 减少批次大小
    formula_recognition_batch_size=1
)
```

### 3. 处理速度慢
```python
# 启用高性能推理
pipeline = PPDocTranslation(
    enable_hpi=True,
    use_tensorrt=True,  # 需要GPU支持
    precision="fp16"    # 使用半精度
)
```

## 与PP-StructureV3的对比

| 特性 | PP-StructureV3 | PP-DocTranslation |
|------|----------------|-------------------|
| 文档解析 | ✅ | ✅ |
| 表格识别 | ✅ | ✅ |
| 公式识别 | ✅ | ✅ |
| 印章识别 | ✅ | ✅ |
| 文档翻译 | ❌ | ✅ |
| 多格式输出 | ✅ | ✅ |
| 端到端处理 | 部分 | ✅ |
| 大模型集成 | ❌ | ✅ |

## 总结

PP-DocTranslation 是 PaddleOCR 最新的文档处理产线，它不仅包含了 PP-StructureV3 的所有功能，还增加了文档翻译能力。对于需要完整文档处理流程的应用，推荐使用 PP-DocTranslation。

更多详细信息请参考：
- [PaddleOCR官方文档](https://www.paddleocr.ai/latest/version3.x/pipeline_usage/PP-DocTranslation.html)
- [PP-StructureV3使用指南](PP_STRUCTUREV3_USAGE_GUIDE.md)
