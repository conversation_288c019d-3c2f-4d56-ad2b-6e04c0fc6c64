#!/usr/bin/env python3
"""
性能和基准测试
测试OCR处理的性能指标和资源使用情况
"""

import sys
import os
import time
import statistics
from pathlib import Path
from typing import Dict, Any, List, Tuple

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from test_utils import TestUtils, OCRTestRunner


def test_initialization_performance() -> bool:
    """测试初始化性能"""
    print("测试OCR引擎初始化性能...")
    
    try:
        from core.ocr_engine import OCREngine
        from config.settings import AppSettings
        from config.models_config import ModelsConfig
        import psutil
        
        # 记录初始状态
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 创建配置
        settings = AppSettings()
        models_config = ModelsConfig()
        
        # 测试多次初始化的性能
        initialization_times = []
        
        for attempt in range(3):
            print(f"初始化尝试 {attempt + 1}/3...")
            
            # 创建新的OCR引擎实例
            ocr_engine = OCREngine(settings.ocr, models_config)
            
            # 设置回调函数
            progress_updates = []
            
            def progress_callback(progress: int, status: str):
                progress_updates.append((time.time(), progress, status))
            
            ocr_engine.progress_callback = progress_callback
            
            # 测量初始化时间
            start_time = time.time()
            
            try:
                with TestUtils.timeout_context(300):  # 5分钟超时
                    success = ocr_engine.initialize()
                
                end_time = time.time()
                init_time = end_time - start_time
                
                if success:
                    initialization_times.append(init_time)
                    print(f"  ✅ 初始化成功，耗时: {init_time:.2f}s")
                    print(f"  - 进度更新次数: {len(progress_updates)}")
                    
                    # 分析进度更新
                    if progress_updates:
                        first_update = progress_updates[0]
                        last_update = progress_updates[-1]
                        total_progress_time = last_update[0] - first_update[0]
                        print(f"  - 进度跟踪耗时: {total_progress_time:.2f}s")
                    
                    # 清理引擎以准备下次测试
                    del ocr_engine
                    
                    # 只测试一次成功的初始化
                    break
                else:
                    print(f"  ❌ 初始化失败")
                    
            except TimeoutError:
                print(f"  ❌ 初始化超时")
                return False
            except Exception as e:
                print(f"  ❌ 初始化异常: {e}")
                return False
        
        # 分析初始化性能
        if initialization_times:
            avg_time = statistics.mean(initialization_times)
            print(f"✅ 平均初始化时间: {avg_time:.2f}s")
            
            # 检查内存使用
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = final_memory - initial_memory
            print(f"✅ 内存增加: {memory_increase:.1f} MB")
            
            # 性能评估
            if avg_time < 60:
                print("✅ 初始化性能良好")
            elif avg_time < 120:
                print("⚠️  初始化性能一般")
            else:
                print("⚠️  初始化性能较慢")
            
            return True
        else:
            print("❌ 没有成功的初始化")
            return False
        
    except Exception as e:
        print(f"❌ 初始化性能测试异常: {e}")
        return False


def test_image_processing_performance() -> bool:
    """测试图像处理性能"""
    print("测试图像处理性能...")
    
    try:
        from utils.image_utils import ImageUtils
        import psutil
        
        # 获取测试文件
        test_files = TestUtils.get_test_files()
        
        if 'png' not in test_files:
            print("⚠️  图像测试文件不存在，跳过图像处理性能测试")
            return True
        
        image_path = test_files['png']
        print(f"使用测试文件: {image_path.name}")
        
        # 记录初始内存
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 测试各种图像操作的性能
        performance_results = {}
        
        # 1. 图像加载性能
        load_times = []
        for i in range(5):
            start_time = time.time()
            image = ImageUtils.load_image_pil(image_path)
            end_time = time.time()
            load_times.append(end_time - start_time)
        
        avg_load_time = statistics.mean(load_times)
        performance_results['图像加载'] = avg_load_time
        print(f"✅ 平均加载时间: {avg_load_time:.3f}s")
        
        # 2. 格式转换性能
        pil_image = ImageUtils.load_image_pil(image_path)
        cv_image = ImageUtils.load_image(image_path)
        
        conversion_times = []
        for i in range(5):
            start_time = time.time()
            converted_cv = ImageUtils.pil_to_cv2(pil_image)
            converted_pil = ImageUtils.cv2_to_pil(cv_image)
            end_time = time.time()
            conversion_times.append(end_time - start_time)
        
        avg_conversion_time = statistics.mean(conversion_times)
        performance_results['格式转换'] = avg_conversion_time
        print(f"✅ 平均转换时间: {avg_conversion_time:.3f}s")
        
        # 3. 图像预处理性能
        preprocessing_times = []
        for i in range(5):
            start_time = time.time()
            enhanced = ImageUtils.enhance_image_for_ocr(cv_image)
            end_time = time.time()
            preprocessing_times.append(end_time - start_time)
        
        avg_preprocessing_time = statistics.mean(preprocessing_times)
        performance_results['图像预处理'] = avg_preprocessing_time
        print(f"✅ 平均预处理时间: {avg_preprocessing_time:.3f}s")
        
        # 4. 图像调整大小性能
        resize_times = []
        for i in range(5):
            start_time = time.time()
            resized = ImageUtils.resize_image(cv_image, width=800, height=600)
            end_time = time.time()
            resize_times.append(end_time - start_time)
        
        avg_resize_time = statistics.mean(resize_times)
        performance_results['图像调整大小'] = avg_resize_time
        print(f"✅ 平均调整大小时间: {avg_resize_time:.3f}s")
        
        # 5. 图像旋转性能
        rotation_times = []
        for i in range(3):  # 旋转操作较慢，减少测试次数
            start_time = time.time()
            rotated = ImageUtils.rotate_image(cv_image, 45)
            end_time = time.time()
            rotation_times.append(end_time - start_time)
        
        avg_rotation_time = statistics.mean(rotation_times)
        performance_results['图像旋转'] = avg_rotation_time
        print(f"✅ 平均旋转时间: {avg_rotation_time:.3f}s")
        
        # 检查内存使用
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        print(f"✅ 内存增加: {memory_increase:.1f} MB")
        
        # 性能总结
        print("图像处理性能总结:")
        for operation, avg_time in performance_results.items():
            if avg_time < 0.1:
                status = "优秀"
            elif avg_time < 0.5:
                status = "良好"
            elif avg_time < 1.0:
                status = "一般"
            else:
                status = "较慢"
            print(f"  - {operation}: {avg_time:.3f}s ({status})")
        
        return True
        
    except Exception as e:
        print(f"❌ 图像处理性能测试异常: {e}")
        return False


def test_pdf_processing_performance() -> bool:
    """测试PDF处理性能"""
    print("测试PDF处理性能...")
    
    try:
        from utils.image_utils import ImageUtils
        import psutil
        
        # 获取测试文件
        test_files = TestUtils.get_test_files()
        
        if 'pdf' not in test_files:
            print("⚠️  PDF测试文件不存在，跳过PDF处理性能测试")
            return True
        
        pdf_path = test_files['pdf']
        print(f"使用测试文件: {pdf_path.name}")
        
        # 记录初始内存
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 测试不同DPI的转换性能
        dpi_settings = [100, 150, 200]
        performance_results = {}
        
        for dpi in dpi_settings:
            print(f"测试DPI {dpi}...")
            
            conversion_times = []
            memory_usage = []
            
            for attempt in range(3):
                # 记录转换前内存
                before_memory = process.memory_info().rss / 1024 / 1024  # MB
                
                start_time = time.time()
                try:
                    images = ImageUtils.convert_pdf_to_images(
                        pdf_path, dpi=dpi, first_page=1, last_page=2
                    )
                    end_time = time.time()
                    
                    conversion_time = end_time - start_time
                    conversion_times.append(conversion_time)
                    
                    # 记录转换后内存
                    after_memory = process.memory_info().rss / 1024 / 1024  # MB
                    memory_usage.append(after_memory - before_memory)
                    
                    print(f"  尝试 {attempt + 1}: {conversion_time:.2f}s, {len(images)} 页")
                    
                    # 清理图像以释放内存
                    del images
                    
                except Exception as e:
                    print(f"  尝试 {attempt + 1} 失败: {e}")
                    continue
            
            if conversion_times:
                avg_time = statistics.mean(conversion_times)
                avg_memory = statistics.mean(memory_usage)
                
                performance_results[f'DPI_{dpi}'] = {
                    'time': avg_time,
                    'memory': avg_memory
                }
                
                print(f"  ✅ DPI {dpi} 平均: {avg_time:.2f}s, 内存: {avg_memory:.1f}MB")
            else:
                print(f"  ❌ DPI {dpi} 所有尝试都失败")
        
        # 测试页面范围对性能的影响
        if performance_results:
            print("测试页面范围性能...")
            
            try:
                # 测试单页
                start_time = time.time()
                single_page = ImageUtils.convert_pdf_to_images(
                    pdf_path, dpi=150, first_page=1, last_page=1
                )
                single_page_time = time.time() - start_time
                
                # 测试多页（如果PDF有多页）
                start_time = time.time()
                all_pages = ImageUtils.convert_pdf_to_images(pdf_path, dpi=150)
                all_pages_time = time.time() - start_time
                
                print(f"✅ 单页转换: {single_page_time:.2f}s")
                print(f"✅ 全部页面转换: {all_pages_time:.2f}s ({len(all_pages)} 页)")
                
                if len(all_pages) > 1:
                    per_page_time = all_pages_time / len(all_pages)
                    print(f"✅ 平均每页: {per_page_time:.2f}s")
                
                # 清理
                del single_page, all_pages
                
            except Exception as e:
                print(f"⚠️  页面范围性能测试失败: {e}")
        
        # 检查最终内存使用
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        total_memory_increase = final_memory - initial_memory
        print(f"✅ 总内存增加: {total_memory_increase:.1f} MB")
        
        # 性能总结
        if performance_results:
            print("PDF处理性能总结:")
            for setting, metrics in performance_results.items():
                time_status = "优秀" if metrics['time'] < 5 else "良好" if metrics['time'] < 10 else "一般"
                memory_status = "优秀" if metrics['memory'] < 50 else "良好" if metrics['memory'] < 100 else "一般"
                print(f"  - {setting}: {metrics['time']:.2f}s ({time_status}), {metrics['memory']:.1f}MB ({memory_status})")
        
        return True
        
    except Exception as e:
        print(f"❌ PDF处理性能测试异常: {e}")
        return False


def test_ocr_processing_performance() -> bool:
    """测试OCR处理性能"""
    print("测试OCR处理性能...")
    
    try:
        from core.ocr_engine import OCREngine
        from config.settings import AppSettings
        from config.models_config import ModelsConfig
        from utils.image_utils import ImageUtils
        import psutil
        
        # 获取测试文件
        test_files = TestUtils.get_test_files()
        
        if not test_files:
            print("⚠️  没有测试文件，跳过OCR处理性能测试")
            return True
        
        # 创建并初始化OCR引擎
        settings = AppSettings()
        models_config = ModelsConfig()
        ocr_engine = OCREngine(settings.ocr, models_config)
        
        # 初始化OCR引擎
        print("初始化OCR引擎...")
        try:
            with TestUtils.timeout_context(300):
                init_success = ocr_engine.initialize()
            
            if not init_success:
                print("❌ OCR引擎初始化失败")
                return False
                
        except TimeoutError:
            print("❌ OCR引擎初始化超时")
            return False
        
        # 记录初始内存
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        performance_results = {}
        
        # 测试图像OCR性能
        if 'png' in test_files:
            print("测试图像OCR性能...")
            image_path = test_files['png']
            pil_image = ImageUtils.load_image_pil(image_path)
            
            # 测试通用模式
            ocr_engine.set_mode('general')
            
            ocr_times = []
            for attempt in range(3):
                start_time = time.time()
                result = ocr_engine.process_image(pil_image, page_number=1)
                end_time = time.time()
                
                ocr_time = end_time - start_time
                ocr_times.append(ocr_time)
                
                # 验证结果
                validation = TestUtils.validate_ocr_result(result)
                print(f"  尝试 {attempt + 1}: {ocr_time:.2f}s, 文本块: {validation['text_count']}")
            
            avg_ocr_time = statistics.mean(ocr_times)
            performance_results['图像OCR(通用)'] = avg_ocr_time
            print(f"✅ 图像OCR平均时间: {avg_ocr_time:.2f}s")
            
            # 测试表格模式
            ocr_engine.set_mode('table')
            
            table_times = []
            for attempt in range(2):  # 表格模式较慢，减少测试次数
                start_time = time.time()
                result = ocr_engine.process_image(pil_image, page_number=1)
                end_time = time.time()
                
                table_time = end_time - start_time
                table_times.append(table_time)
                
                validation = TestUtils.validate_ocr_result(result)
                print(f"  表格模式尝试 {attempt + 1}: {table_time:.2f}s, 文本块: {validation['text_count']}")
            
            avg_table_time = statistics.mean(table_times)
            performance_results['图像OCR(表格)'] = avg_table_time
            print(f"✅ 表格OCR平均时间: {avg_table_time:.2f}s")
        
        # 测试PDF OCR性能
        if 'pdf' in test_files:
            print("测试PDF OCR性能...")
            pdf_path = test_files['pdf']
            
            # 转换PDF第一页
            images = ImageUtils.convert_pdf_to_images(
                pdf_path, dpi=150, first_page=1, last_page=1
            )
            
            if images:
                ocr_engine.set_mode('general')
                
                pdf_ocr_times = []
                for attempt in range(2):
                    start_time = time.time()
                    result = ocr_engine.process_image(images[0], page_number=1)
                    end_time = time.time()
                    
                    pdf_ocr_time = end_time - start_time
                    pdf_ocr_times.append(pdf_ocr_time)
                    
                    validation = TestUtils.validate_ocr_result(result)
                    print(f"  PDF OCR尝试 {attempt + 1}: {pdf_ocr_time:.2f}s, 文本块: {validation['text_count']}")
                
                avg_pdf_ocr_time = statistics.mean(pdf_ocr_times)
                performance_results['PDF OCR'] = avg_pdf_ocr_time
                print(f"✅ PDF OCR平均时间: {avg_pdf_ocr_time:.2f}s")
        
        # 检查内存使用
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        print(f"✅ OCR处理内存增加: {memory_increase:.1f} MB")
        
        # 性能总结
        if performance_results:
            print("OCR处理性能总结:")
            for operation, avg_time in performance_results.items():
                if avg_time < 5:
                    status = "优秀"
                elif avg_time < 15:
                    status = "良好"
                elif avg_time < 30:
                    status = "一般"
                else:
                    status = "较慢"
                print(f"  - {operation}: {avg_time:.2f}s ({status})")
        
        return True
        
    except Exception as e:
        print(f"❌ OCR处理性能测试异常: {e}")
        return False


def test_memory_usage_patterns() -> bool:
    """测试内存使用模式"""
    print("测试内存使用模式...")
    
    try:
        import psutil
        import gc
        from utils.image_utils import ImageUtils
        
        process = psutil.Process()
        
        # 记录基线内存
        gc.collect()
        baseline_memory = process.memory_info().rss / 1024 / 1024  # MB
        print(f"基线内存: {baseline_memory:.1f} MB")
        
        memory_snapshots = [baseline_memory]
        
        # 获取测试文件
        test_files = TestUtils.get_test_files()
        
        if test_files:
            # 测试图像加载的内存模式
            if 'png' in test_files:
                print("测试图像加载内存模式...")
                
                # 加载多个图像副本
                images = []
                for i in range(5):
                    image = ImageUtils.load_image_pil(test_files['png'])
                    images.append(image)
                    
                    current_memory = process.memory_info().rss / 1024 / 1024  # MB
                    memory_snapshots.append(current_memory)
                    print(f"  加载图像 {i+1}: {current_memory:.1f} MB (+{current_memory - baseline_memory:.1f})")
                
                # 清理图像
                images.clear()
                del images
                gc.collect()
                
                after_cleanup_memory = process.memory_info().rss / 1024 / 1024  # MB
                memory_snapshots.append(after_cleanup_memory)
                print(f"  清理后: {after_cleanup_memory:.1f} MB")
            
            # 测试PDF转换的内存模式
            if 'pdf' in test_files:
                print("测试PDF转换内存模式...")
                
                before_pdf_memory = process.memory_info().rss / 1024 / 1024  # MB
                
                # 转换PDF
                pdf_images = ImageUtils.convert_pdf_to_images(test_files['pdf'], dpi=150)
                
                after_pdf_memory = process.memory_info().rss / 1024 / 1024  # MB
                memory_snapshots.append(after_pdf_memory)
                print(f"  PDF转换后: {after_pdf_memory:.1f} MB (+{after_pdf_memory - before_pdf_memory:.1f})")
                
                # 清理PDF图像
                del pdf_images
                gc.collect()
                
                after_pdf_cleanup_memory = process.memory_info().rss / 1024 / 1024  # MB
                memory_snapshots.append(after_pdf_cleanup_memory)
                print(f"  PDF清理后: {after_pdf_cleanup_memory:.1f} MB")
        
        # 分析内存使用模式
        max_memory = max(memory_snapshots)
        min_memory = min(memory_snapshots)
        memory_range = max_memory - min_memory
        
        print(f"✅ 内存使用分析:")
        print(f"  - 最大内存: {max_memory:.1f} MB")
        print(f"  - 最小内存: {min_memory:.1f} MB")
        print(f"  - 内存范围: {memory_range:.1f} MB")
        
        # 检查内存泄漏
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_leak = final_memory - baseline_memory
        
        if memory_leak < 10:
            print(f"✅ 内存使用良好，增加: {memory_leak:.1f} MB")
        elif memory_leak < 50:
            print(f"⚠️  内存使用一般，增加: {memory_leak:.1f} MB")
        else:
            print(f"⚠️  可能存在内存泄漏，增加: {memory_leak:.1f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ 内存使用模式测试异常: {e}")
        return False


def main():
    """主测试函数"""
    print("性能和基准测试")
    print("=" * 50)
    
    # 创建测试运行器
    runner = OCRTestRunner()
    runner.setup()
    
    try:
        # 定义测试用例
        test_cases = [
            (test_initialization_performance, "初始化性能"),
            (test_image_processing_performance, "图像处理性能"),
            (test_pdf_processing_performance, "PDF处理性能"),
            (test_ocr_processing_performance, "OCR处理性能"),
            (test_memory_usage_patterns, "内存使用模式"),
        ]
        
        # 运行测试
        for test_func, test_name in test_cases:
            print(f"\n{'='*20} {test_name} {'='*20}")
            runner.run_test(test_func, test_name)
        
        # 生成报告
        print("\n" + "="*60)
        print(runner.generate_report())
        
        # 保存报告
        runner.save_report("performance_test_report.txt")
        
        # 返回结果
        passed = sum(1 for r in runner.results if r['success'])
        total = len(runner.results)
        
        if passed == total:
            print(f"\n🎉 所有测试通过！性能表现正常。")
            return 0
        else:
            print(f"\n⚠️  {passed}/{total} 测试通过，请检查失败的测试。")
            return 1
    
    finally:
        runner.cleanup()


if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程发生未知错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
