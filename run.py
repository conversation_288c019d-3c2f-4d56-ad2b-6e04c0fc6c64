#!/usr/bin/env python3
"""
Prisma OCR & Translator 启动脚本
用于开发和测试
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        sys.exit(1)

def check_dependencies():
    """检查基本依赖"""
    missing_deps = []
    
    try:
        import PyQt6
    except ImportError:
        missing_deps.append("PyQt6")
    
    try:
        import paddle
    except ImportError:
        missing_deps.append("paddlepaddle")
    
    try:
        import paddleocr
    except ImportError:
        missing_deps.append("paddleocr")
    
    if missing_deps:
        print("错误: 缺少以下依赖包:")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        sys.exit(1)

def main():
    """主函数"""
    print("Prisma OCR & Translator 启动中...")
    
    # 检查Python版本
    check_python_version()
    
    # 检查依赖
    check_dependencies()
    
    # 导入并运行主程序
    try:
        from main import main as app_main
        app_main()
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
