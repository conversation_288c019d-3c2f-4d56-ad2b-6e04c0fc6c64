#!/usr/bin/env python3
"""
Prisma OCR & Translator - 本地PDF与图像OCR及翻译工具
主程序入口点 (基于pywebview)

Author: AI Assistant
Date: 2025-09-11
Version: 2.0.0
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from webui.web_controller import WebController
from utils.compatibility_check import CompatibilityChecker
from utils.system_monitor import SystemMonitor


class PyWebViewLogFilter(logging.Filter):
    """PyWebView日志过滤器，屏蔽内部错误和递归问题"""

    def filter(self, record):
        # 屏蔽pywebview的内部错误
        if record.name == 'pywebview':
            message = record.getMessage()

            # 屏蔽递归深度错误
            if 'maximum recursion depth exceeded' in message:
                return False

            # 屏蔽Empty属性访问错误
            if 'Empty.Empty.Empty' in message:
                return False

            # 屏蔽Windows控件相关错误
            if any(keyword in message for keyword in [
                'ControlCollection.__abstractmethods__',
                'DockPaddingEdgesConverter',
                'CoreWebView2 members can only be accessed from the UI thread',
                'pathlib._local.WindowsPath',
                '_hash',
                '_resolving'
            ]):
                return False

            # 屏蔽COM接口错误
            if any(keyword in message for keyword in [
                'ICoreWebView2Controller',
                'E_NOINTERFACE',
                'QueryInterface',
                'System.__ComObject'
            ]):
                return False

        return True


def setup_logging():
    """设置日志系统"""
    log_dir = project_root / "logs"
    log_dir.mkdir(exist_ok=True)

    # 创建自定义格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # 文件处理器
    file_handler = logging.FileHandler(
        log_dir / "prisma_ocr.log",
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    file_handler.addFilter(PyWebViewLogFilter())

    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    console_handler.addFilter(PyWebViewLogFilter())

    # 配置根日志记录器
    logging.basicConfig(
        level=logging.INFO,
        handlers=[file_handler, console_handler]
    )

    # 特别设置pywebview日志级别为CRITICAL，最大程度减少噪音
    logging.getLogger('pywebview').setLevel(logging.CRITICAL)

    # 抑制pywebview的直接输出
    try:
        import webview
        # 如果pywebview有设置日志级别的方法，使用它
        if hasattr(webview, 'logger'):
            webview.logger.setLevel(logging.CRITICAL)
    except Exception:
        pass

    # 清理旧日志文件
    try:
        system_monitor = SystemMonitor()
        cleanup_result = system_monitor.cleanup_logs(log_dir, max_size_mb=50, max_files=5)
        if cleanup_result['success'] and cleanup_result.get('cleaned_files'):
            print(f"日志清理完成: {cleanup_result['message']}")
    except Exception as e:
        print(f"日志清理失败: {e}")


def check_dependencies():
    """检查必要的依赖是否安装"""
    missing_deps = []

    try:
        import webview
    except ImportError:
        missing_deps.append("pywebview")

    try:
        import paddle
    except ImportError:
        missing_deps.append("paddlepaddle")

    try:
        import paddleocr
    except ImportError:
        missing_deps.append("paddleocr")

    if missing_deps:
        print("错误: 缺少以下依赖包:")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False

    return True


def main():
    """主函数"""
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)
    logger.info("启动 Prisma OCR & Translator (WebView版本)")

    # 运行兼容性检查
    try:
        checker = CompatibilityChecker()
        compat_results = checker.check_all()

        if compat_results['overall_status'] == 'error':
            print("\n❌ 系统兼容性检查失败:")
            for issue in compat_results['issues']:
                print(f"  - {issue}")
            print("\n请解决上述问题后重试。")
            sys.exit(1)
        elif compat_results['overall_status'] == 'warning':
            print("\n⚠️ 系统兼容性警告:")
            for warning in compat_results['warnings']:
                print(f"  - {warning}")
            print("\n应用程序将继续启动，但可能存在性能或功能问题。")
        else:
            logger.info("系统兼容性检查通过")

    except Exception as e:
        logger.warning(f"兼容性检查失败: {e}")

    # 检查依赖
    if not check_dependencies():
        sys.exit(1)

    try:
        # 创建Web控制器
        debug_mode = "--debug" in sys.argv
        web_controller = WebController(debug=debug_mode)

        logger.info("Web应用程序启动成功")

        # 启动Web应用程序
        web_controller.start_application()

    except Exception as e:
        logger.error(f"应用程序启动失败: {e}")
        print(f"启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
