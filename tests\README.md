# 测试文件夹

本文件夹包含Prisma OCR & Translator项目的所有测试脚本。

## 测试脚本分类

### 基础功能测试
- `test_basic.py` - 基础模块导入和功能测试
- `test_simple.py` - 简化的模块导入测试
- `test_dependencies.py` - 依赖包检查测试

### OCR功能测试
- `test_ocr_pdf.py` - PDF OCR功能测试
- `test_ocr_image.py` - 图像OCR功能测试
- `process_pdf.py` - 完整的PDF处理脚本

### 组件测试
- `test_gui_components.py` - GUI组件测试
- `test_config.py` - 配置系统测试
- `test_file_utils.py` - 文件工具测试
- `test_image_utils.py` - 图像工具测试
- `test_system_monitor.py` - 系统监控测试

### 集成测试
- `test_integration.py` - 完整功能集成测试
- `test_performance.py` - 性能测试

## 运行测试

### 运行所有测试
```bash
python tests/run_all_tests.py
```

### 运行特定测试
```bash
# 基础功能测试
python tests/test_basic.py

# OCR功能测试
python tests/test_ocr_pdf.py

# PDF处理测试
python tests/process_pdf.py
```

## 测试数据

测试使用的数据文件：
- `../ocr测试.pdf` - 项目根目录下的测试PDF文件
- 其他测试图像文件（如需要）

## 注意事项

1. 运行OCR相关测试前，请确保已安装PaddleOCR相关依赖
2. 某些测试可能需要GPU支持，如无GPU会自动降级到CPU模式
3. 首次运行OCR测试时会下载模型文件，请确保网络连接正常
