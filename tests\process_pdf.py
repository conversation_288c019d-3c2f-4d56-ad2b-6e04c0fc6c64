#!/usr/bin/env python3
"""
PDF处理脚本
直接处理ocr测试.pdf文件并输出结果
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def process_pdf_with_ocr():
    """使用OCR处理PDF文件"""
    print("Prisma OCR - PDF处理")
    print("=" * 40)
    
    pdf_path = project_root / "ocr测试.pdf"
    if not pdf_path.exists():
        print("❌ 找不到测试PDF文件: ocr测试.pdf")
        return False
    
    print(f"📄 处理文件: {pdf_path.name}")
    
    try:
        # 检查基本依赖
        print("\n🔍 检查依赖...")
        try:
            import numpy as np
            from PIL import Image
            import cv2
            print("✓ 基础图像处理库可用")
        except ImportError as e:
            print(f"❌ 缺少基础依赖: {e}")
            print("请运行: pip install numpy pillow opencv-python")
            return False
        
        try:
            import pdf2image
            print("✓ PDF处理库可用")
        except ImportError:
            print("❌ 缺少pdf2image库")
            print("请运行: pip install pdf2image")
            return False
        
        # 步骤1: 转换PDF为图像
        print("\n📖 步骤1: 转换PDF为图像...")
        try:
            from utils.image_utils import ImageUtils
            
            start_time = time.time()
            images = ImageUtils.convert_pdf_to_images(pdf_path, dpi=200)
            convert_time = time.time() - start_time
            
            print(f"✓ 转换完成，共 {len(images)} 页，耗时 {convert_time:.2f}秒")
            
            for i, img in enumerate(images):
                print(f"  页面 {i+1}: {img.size[0]}x{img.size[1]} 像素")
                
        except Exception as e:
            print(f"❌ PDF转换失败: {e}")
            return False
        
        # 步骤2: OCR识别
        print("\n🔍 步骤2: OCR文字识别...")
        
        # 检查PaddleOCR
        try:
            import paddle
            import paddleocr
            print("✓ PaddleOCR库可用")
        except ImportError:
            print("❌ PaddleOCR不可用，尝试安装:")
            print("pip install paddlepaddle paddleocr")
            return False
        
        try:
            from paddleocr import PaddleOCR
            
            # 初始化OCR
            print("正在初始化OCR引擎...")
            ocr = PaddleOCR(
                use_angle_cls=True,
                lang='ch',
                show_log=False,
                use_gpu=False  # 先使用CPU模式确保兼容性
            )
            print("✓ OCR引擎初始化完成")
            
            # 处理每一页
            all_results = []
            total_text = ""
            
            for page_num, image in enumerate(images, 1):
                print(f"\n处理第 {page_num} 页...")
                
                # 转换为OpenCV格式
                cv_image = ImageUtils.pil_to_cv2(image)
                
                # 执行OCR
                start_time = time.time()
                result = ocr.ocr(cv_image, cls=True)
                ocr_time = time.time() - start_time
                
                print(f"✓ 第 {page_num} 页识别完成，耗时 {ocr_time:.2f}秒")
                
                # 解析结果
                page_text = ""
                if result and result[0]:
                    print(f"  识别到 {len(result[0])} 个文本块")
                    
                    for line in result[0]:
                        if len(line) >= 2:
                            text = line[1][0] if isinstance(line[1], (list, tuple)) else str(line[1])
                            confidence = line[1][1] if isinstance(line[1], (list, tuple)) and len(line[1]) > 1 else 0.0
                            
                            if confidence > 0.5:  # 只保留置信度较高的结果
                                page_text += text + "\n"
                    
                    all_results.append({
                        'page': page_num,
                        'text': page_text,
                        'blocks': len(result[0])
                    })
                    total_text += f"\n=== 第 {page_num} 页 ===\n" + page_text
                else:
                    print(f"  第 {page_num} 页未识别到文本")
                    all_results.append({
                        'page': page_num,
                        'text': "",
                        'blocks': 0
                    })
            
        except Exception as e:
            print(f"❌ OCR识别失败: {e}")
            return False
        
        # 步骤3: 保存结果
        print("\n💾 步骤3: 保存识别结果...")
        
        try:
            # 生成输出文件名
            output_file = pdf_path.parent / f"{pdf_path.stem}_ocr_result.txt"
            
            # 写入结果
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("Prisma OCR 识别结果\n")
                f.write("=" * 40 + "\n")
                f.write(f"源文件: {pdf_path.name}\n")
                f.write(f"处理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"总页数: {len(images)}\n")
                f.write("=" * 40 + "\n\n")
                f.write(total_text)
                
                # 添加统计信息
                f.write("\n\n" + "=" * 40 + "\n")
                f.write("识别统计:\n")
                for result in all_results:
                    f.write(f"第 {result['page']} 页: {result['blocks']} 个文本块\n")
            
            print(f"✓ 结果已保存到: {output_file}")
            
            # 显示部分结果预览
            print("\n📋 识别结果预览:")
            print("-" * 40)
            preview_text = total_text[:500]  # 显示前500个字符
            print(preview_text)
            if len(total_text) > 500:
                print("...")
                print(f"(完整结果共 {len(total_text)} 个字符，已保存到文件)")
            
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")
            return False
        
        print("\n🎉 PDF处理完成！")
        print(f"📊 处理统计:")
        print(f"  - 总页数: {len(images)}")
        print(f"  - 识别文本块: {sum(r['blocks'] for r in all_results)}")
        print(f"  - 输出文件: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理过程发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = process_pdf_with_ocr()
    return 0 if success else 1

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n处理被用户中断")
        sys.exit(1)
