#!/usr/bin/env python3
"""
测试图像格式问题
"""

from utils.image_utils import ImageUtils
import numpy as np
import cv2

def test_image_format():
    print("=== 测试图像格式问题 ===")
    
    # 测试图像
    test_image = 'ocr测试.PNG'
    
    # 1. 检查PIL图像
    print(f"\n1. 检查PIL图像格式:")
    pil_image = ImageUtils.load_image_pil(test_image)
    print(f"   PIL模式: {pil_image.mode}")
    print(f"   PIL尺寸: {pil_image.size}")
    
    # 转换为numpy数组
    img_array = np.array(pil_image)
    print(f"   numpy形状: {img_array.shape}")
    print(f"   numpy数据类型: {img_array.dtype}")
    
    # 2. 检查OpenCV图像
    print(f"\n2. 检查OpenCV图像格式:")
    try:
        cv_image = ImageUtils.load_image(test_image)
        print(f"   OpenCV形状: {cv_image.shape}")
        print(f"   OpenCV数据类型: {cv_image.dtype}")
    except Exception as e:
        print(f"   OpenCV加载失败: {e}")
    
    # 3. 测试不同的图像格式转换
    print(f"\n3. 测试图像格式转换:")
    
    # 确保图像是RGB格式
    if pil_image.mode != 'RGB':
        print(f"   转换 {pil_image.mode} -> RGB")
        pil_image = pil_image.convert('RGB')
    
    # 转换为numpy数组
    rgb_array = np.array(pil_image)
    print(f"   RGB数组形状: {rgb_array.shape}")
    
    # 4. 测试OCR处理
    print(f"\n4. 测试OCR处理:")
    
    test_formats = [
        ("原始PIL转numpy", np.array(pil_image)),
        ("RGB格式", rgb_array),
        ("BGR格式", cv2.cvtColor(rgb_array, cv2.COLOR_RGB2BGR)),
    ]
    
    for format_name, img_data in test_formats:
        print(f"\n   测试格式: {format_name}")
        print(f"   形状: {img_data.shape}")
        print(f"   数据类型: {img_data.dtype}")
        print(f"   数值范围: {img_data.min()} - {img_data.max()}")
        
        try:
            from paddleocr import PaddleOCR
            
            # 创建OCR实例
            ocr = PaddleOCR(lang='ch')
            
            # 尝试OCR处理
            result = ocr.ocr(img_data)
            
            if result and result[0]:
                text_blocks = result[0]
                print(f"   ✓ 成功识别 {len(text_blocks)} 个文本块")
                
                for i, block in enumerate(text_blocks[:3]):
                    if len(block) >= 2:
                        text = block[1][0] if block[1] else ""
                        confidence = block[1][1] if len(block[1]) > 1 else 0.0
                        print(f"     {i+1}. {text} (置信度: {confidence:.3f})")
                        
                if len(text_blocks) > 0:
                    print(f"   🎉 格式 {format_name} 成功！")
                    return format_name, img_data  # 返回成功的格式
            else:
                print(f"   ✗ 未识别到文本")
                
        except Exception as e:
            print(f"   ✗ 处理失败: {e}")
    
    # 5. 测试保存和重新加载
    print(f"\n5. 测试保存和重新加载:")
    
    try:
        # 保存为标准格式
        cv2.imwrite("debug_standard_format.jpg", cv2.cvtColor(rgb_array, cv2.COLOR_RGB2BGR))
        print("   已保存标准格式图像: debug_standard_format.jpg")
        
        # 重新加载
        reloaded = cv2.imread("debug_standard_format.jpg")
        print(f"   重新加载形状: {reloaded.shape}")
        
        # 测试重新加载的图像
        from paddleocr import PaddleOCR
        ocr = PaddleOCR(lang='ch')
        result = ocr.ocr(reloaded)
        
        if result and result[0]:
            text_blocks = result[0]
            print(f"   ✓ 重新加载后识别 {len(text_blocks)} 个文本块")
            
            for i, block in enumerate(text_blocks[:3]):
                if len(block) >= 2:
                    text = block[1][0] if block[1] else ""
                    confidence = block[1][1] if len(block[1]) > 1 else 0.0
                    print(f"     {i+1}. {text} (置信度: {confidence:.3f})")
                    
            if len(text_blocks) > 0:
                print(f"   🎉 重新加载成功！")
        else:
            print(f"   ✗ 重新加载后未识别到文本")
            
    except Exception as e:
        print(f"   ✗ 保存/重新加载失败: {e}")
    
    print(f"\n=== 测试完成 ===")

if __name__ == "__main__":
    test_image_format()
