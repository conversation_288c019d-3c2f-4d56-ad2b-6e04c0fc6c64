#!/usr/bin/env python3
"""
快速OCR诊断测试
"""

from utils.ocr_diagnostics import OCRDiagnostics
from core.ocr_engine import OCREngine
from config.settings import AppSettings
from config.models_config import ModelsConfig
from utils.image_utils import ImageUtils
import time

def main():
    print("=== 快速OCR诊断测试 ===")
    
    # 测试图像
    test_image = 'ocr测试.PNG'
    
    # 1. 图像质量诊断
    print("\n1. 图像质量诊断:")
    diagnostics = OCRDiagnostics()
    diagnosis = diagnostics.diagnose_image_quality(test_image)
    
    if 'error' not in diagnosis:
        print(f"   整体质量: {diagnosis['overall_quality']}")
        print(f"   图像尺寸: {diagnosis['basic_info']['width']}x{diagnosis['basic_info']['height']}")
        print(f"   亮度状态: {diagnosis['brightness']['status']} (均值: {diagnosis['brightness']['mean']:.1f})")
        print(f"   对比度状态: {diagnosis['contrast']['status']} (比值: {diagnosis['contrast']['ratio']:.3f})")
        print(f"   清晰度状态: {diagnosis['sharpness']['status']}")
        print(f"   文本密度状态: {diagnosis['text_density']['status']} (密度: {diagnosis['text_density']['value']:.3f})")
    else:
        print(f"   诊断失败: {diagnosis['error']}")
    
    # 2. OCR参数测试
    print("\n2. OCR参数测试:")
    try:
        param_test = diagnostics.test_ocr_parameters(test_image)
        
        if 'error' not in param_test:
            for name, result in param_test.items():
                if result['success']:
                    print(f"   {name}: {result['text_blocks']} 个文本块, 平均置信度: {result['avg_confidence']:.3f}")
                else:
                    print(f"   {name}: 失败 - {result.get('error', '未知错误')}")
        else:
            print(f"   测试失败: {param_test['error']}")
    except Exception as e:
        print(f"   参数测试异常: {e}")
    
    # 3. 生成完整诊断报告
    print("\n3. 生成诊断报告:")
    try:
        report_path = diagnostics.generate_diagnostic_report(test_image)
        if report_path:
            print(f"   诊断报告已生成: {report_path}")
        else:
            print("   诊断报告生成失败")
    except Exception as e:
        print(f"   报告生成异常: {e}")
    
    # 4. 直接OCR测试
    print("\n4. 直接OCR测试:")
    try:
        settings = AppSettings()
        models_config = ModelsConfig()
        ocr_engine = OCREngine(settings.ocr, models_config)
        
        print("   正在初始化OCR引擎...")
        if ocr_engine.initialize():
            print("   OCR引擎初始化成功")
            
            image = ImageUtils.load_image_pil(test_image)
            start_time = time.time()
            result = ocr_engine.process_image(image, page_number=1)
            end_time = time.time()
            
            print(f"   处理完成，耗时: {end_time - start_time:.2f}s")
            print(f"   识别到的文本块数: {len(result.text_blocks)}")
            
            if result.text_blocks:
                print("   识别结果:")
                for i, block in enumerate(result.text_blocks[:3]):
                    text = block.get('text', '').strip()
                    confidence = block.get('confidence', 0.0)
                    print(f"     {i+1}. {text} (置信度: {confidence:.3f})")
            else:
                print("   未识别到文本内容")
        else:
            print("   OCR引擎初始化失败")
            
    except Exception as e:
        print(f"   OCR测试异常: {e}")
    
    print("\n=== 诊断完成 ===")

if __name__ == "__main__":
    main()
