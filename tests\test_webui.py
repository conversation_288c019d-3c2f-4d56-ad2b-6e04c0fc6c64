#!/usr/bin/env python3
"""
Web UI测试脚本 - 测试pywebview GUI功能
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def check_webview_dependencies():
    """检查pywebview相关依赖"""
    print("检查Web UI依赖...")
    
    missing_deps = []
    
    try:
        import webview
        print(f"✅ pywebview {webview.__version__}")
    except ImportError:
        missing_deps.append("pywebview")
        print("❌ pywebview 未安装")
    
    # 检查Web后端依赖
    try:
        import flask
        print("✅ Flask (Web后端)")
    except ImportError:
        print("⚠️  Flask 未安装 (可选Web后端)")
    
    try:
        import bottle
        print("✅ Bottle (Web后端)")
    except ImportError:
        print("⚠️  Bottle 未安装 (可选Web后端)")
    
    if missing_deps:
        print(f"\n缺少依赖包: {', '.join(missing_deps)}")
        print("请运行: pip install " + " ".join(missing_deps))
        return False
    
    return True


def test_webui_modules():
    """测试Web UI模块导入"""
    print("\n测试Web UI模块导入...")
    
    modules_to_test = [
        ("webui.web_controller", "WebController"),
        ("webui.api_bridge", "APIBridge"),
        ("webui.file_api", "FileAPI"),
        ("webui.ocr_api", "OCRAPI"),
    ]
    
    success_count = 0
    
    for module_name, class_name in modules_to_test:
        try:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            print(f"✅ {module_name}.{class_name}")
            success_count += 1
        except ImportError as e:
            print(f"❌ {module_name}.{class_name} - 导入失败: {e}")
        except AttributeError as e:
            print(f"❌ {module_name}.{class_name} - 类不存在: {e}")
        except Exception as e:
            print(f"❌ {module_name}.{class_name} - 其他错误: {e}")
    
    print(f"\n模块导入成功率: {success_count}/{len(modules_to_test)} ({success_count/len(modules_to_test)*100:.1f}%)")
    return success_count == len(modules_to_test)


def test_web_controller_creation():
    """测试Web控制器创建"""
    print("\n测试Web控制器创建...")
    
    try:
        from webui.web_controller import WebController
        
        # 创建Web控制器实例（调试模式）
        web_controller = WebController(debug=True)
        print("✅ Web控制器创建成功")
        
        # 检查控制器属性
        if hasattr(web_controller, 'api_bridge'):
            print("✅ API桥接器已初始化")
        else:
            print("⚠️  API桥接器未找到")
        
        if hasattr(web_controller, 'debug'):
            print(f"✅ 调试模式: {web_controller.debug}")
        
        return True
        
    except Exception as e:
        print(f"❌ Web控制器创建失败: {e}")
        return False


def test_api_bridge():
    """测试API桥接器"""
    print("\n测试API桥接器...")
    
    try:
        from webui.api_bridge import APIBridge
        
        # 创建API桥接器
        api_bridge = APIBridge()
        print("✅ API桥接器创建成功")
        
        # 检查API方法
        api_methods = [
            'get_system_info',
            'process_files',
            'get_processing_status',
            'cancel_processing'
        ]
        
        for method_name in api_methods:
            if hasattr(api_bridge, method_name):
                print(f"✅ API方法 {method_name} 存在")
            else:
                print(f"⚠️  API方法 {method_name} 不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ API桥接器测试失败: {e}")
        return False


def test_file_api():
    """测试文件API"""
    print("\n测试文件API...")
    
    try:
        from webui.file_api import FileAPI
        
        # 创建文件API实例
        file_api = FileAPI()
        print("✅ 文件API创建成功")
        
        # 测试文件验证功能
        test_files = [
            ("test.pdf", True),
            ("test.png", True),
            ("test.jpg", True),
            ("test.txt", False),
            ("test.doc", False),
        ]
        
        for filename, expected in test_files:
            if hasattr(file_api, 'is_supported_file'):
                try:
                    result = file_api.is_supported_file(filename)
                    if result == expected:
                        print(f"✅ 文件格式检查 {filename}: {result}")
                    else:
                        print(f"⚠️  文件格式检查 {filename}: 期望{expected}, 实际{result}")
                except Exception as e:
                    print(f"❌ 文件格式检查 {filename} 失败: {e}")
            else:
                print("⚠️  文件格式检查方法不存在")
                break
        
        return True
        
    except Exception as e:
        print(f"❌ 文件API测试失败: {e}")
        return False


def test_ocr_api():
    """测试OCR API"""
    print("\n测试OCR API...")
    
    try:
        from webui.ocr_api import OCRAPI
        
        # 创建OCR API实例
        ocr_api = OCRAPI()
        print("✅ OCR API创建成功")
        
        # 检查OCR相关方法
        ocr_methods = [
            'process_image',
            'process_pdf',
            'get_supported_languages',
            'get_processing_modes'
        ]
        
        for method_name in ocr_methods:
            if hasattr(ocr_api, method_name):
                print(f"✅ OCR方法 {method_name} 存在")
            else:
                print(f"⚠️  OCR方法 {method_name} 不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ OCR API测试失败: {e}")
        return False


def test_webview_basic_functionality():
    """测试pywebview基本功能"""
    print("\n测试pywebview基本功能...")
    
    try:
        import webview
        
        # 测试创建窗口配置（不实际显示）
        window_config = {
            'title': 'Prisma OCR Test',
            'width': 800,
            'height': 600,
            'min_size': (600, 400),
            'resizable': True
        }
        
        print("✅ 窗口配置创建成功")
        print(f"  - 标题: {window_config['title']}")
        print(f"  - 尺寸: {window_config['width']}x{window_config['height']}")
        
        # 检查可用的Web引擎
        try:
            # 这个方法可能不存在于所有版本
            if hasattr(webview, 'platforms'):
                print(f"✅ 可用平台: {webview.platforms}")
        except:
            print("⚠️  无法获取平台信息")
        
        return True
        
    except Exception as e:
        print(f"❌ pywebview基本功能测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("Prisma OCR & Translator - Web UI测试")
    print("=" * 50)
    
    tests = [
        ("pywebview依赖", check_webview_dependencies),
        ("Web UI模块", test_webui_modules),
        ("Web控制器", test_web_controller_creation),
        ("API桥接器", test_api_bridge),
        ("文件API", test_file_api),
        ("OCR API", test_ocr_api),
        ("pywebview功能", test_webview_basic_functionality),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"Web UI测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有Web UI测试通过！")
        print("可以尝试运行: python main.py")
        return 0
    elif passed >= total // 2:
        print("⚠️  部分测试通过，基本功能可用")
        return 1
    else:
        print("❌ 多数测试失败，请检查Web UI配置")
        return 2


if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\nWeb UI测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\nWeb UI测试异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
