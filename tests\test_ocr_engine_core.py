#!/usr/bin/env python3
"""
OCR引擎核心功能测试
测试OCR引擎的初始化、配置和基本功能
"""

import sys
import os
import time
from pathlib import Path
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from test_utils import TestUtils, OCRTestRunner


def test_dependencies_available() -> bool:
    """测试依赖包可用性"""
    print("检查OCR相关依赖...")
    
    success, missing_deps = TestUtils.check_dependencies()
    
    if success:
        print("[PASS] 所有依赖包可用")
        return True
    else:
        print(f"[FAIL] 缺少依赖包: {', '.join(missing_deps)}")
        return False


def test_ocr_engine_import() -> bool:
    """测试OCR引擎模块导入"""
    print("测试OCR引擎模块导入...")
    
    try:
        from core.ocr_engine import OCREngine, OCRResult
        from config.settings import AppSettings
        from config.models_config import ModelsConfig
        
        print("✅ OCR引擎模块导入成功")
        return True
        
    except ImportError as e:
        print(f"❌ OCR引擎模块导入失败: {e}")
        return False


def test_configuration_loading() -> bool:
    """测试配置加载"""
    print("测试配置系统...")
    
    try:
        from config.settings import AppSettings
        from config.models_config import ModelsConfig
        
        # 测试应用设置
        settings = AppSettings()
        print(f"✅ 应用设置加载成功")
        print(f"  - OCR语言: {settings.ocr.lang}")
        print(f"  - 使用GPU: {settings.ocr.use_gpu}")
        print(f"  - 使用角度分类: {settings.ocr.use_angle_cls}")
        
        # 测试模型配置
        models_config = ModelsConfig()
        all_models = models_config.get_all_models()
        print(f"✅ 模型配置加载成功，共 {len(all_models)} 个模型")
        
        # 检查模型文件状态
        missing_models = models_config.get_missing_models()
        if missing_models:
            print(f"  ⚠️  缺少 {len(missing_models)} 个模型文件")
            for model_name in list(missing_models.keys())[:3]:  # 只显示前3个
                print(f"    - {model_name}")
            if len(missing_models) > 3:
                print(f"    - ... 还有 {len(missing_models) - 3} 个")
        else:
            print("  ✅ 所有模型文件已就绪")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False


def test_ocr_engine_creation() -> bool:
    """测试OCR引擎创建"""
    print("测试OCR引擎创建...")
    
    try:
        from core.ocr_engine import OCREngine
        from config.settings import AppSettings
        from config.models_config import ModelsConfig
        
        # 创建配置
        settings = AppSettings()
        models_config = ModelsConfig()
        
        # 创建OCR引擎实例
        ocr_engine = OCREngine(settings.ocr, models_config)
        
        print("✅ OCR引擎实例创建成功")
        print(f"  - 当前模式: {ocr_engine.current_mode}")
        print(f"  - 初始化状态: {ocr_engine.is_initialized}")
        
        return True
        
    except Exception as e:
        print(f"❌ OCR引擎创建失败: {e}")
        return False


def test_ocr_engine_initialization() -> bool:
    """测试OCR引擎初始化"""
    print("测试OCR引擎初始化...")
    
    try:
        from core.ocr_engine import OCREngine
        from config.settings import AppSettings
        from config.models_config import ModelsConfig
        
        # 创建配置
        settings = AppSettings()
        models_config = ModelsConfig()
        
        # 创建OCR引擎实例
        ocr_engine = OCREngine(settings.ocr, models_config)
        
        # 设置回调函数来监控初始化进度
        progress_updates = []
        error_messages = []
        
        def progress_callback(progress: int, status: str):
            progress_updates.append((progress, status))
            print(f"  进度: {progress}% - {status}")
        
        def error_callback(error_msg: str):
            error_messages.append(error_msg)
            print(f"  错误: {error_msg}")
        
        ocr_engine.progress_callback = progress_callback
        ocr_engine.error_callback = error_callback
        
        # 尝试初始化（设置超时）
        print("开始初始化OCR引擎...")
        start_time = time.time()
        
        try:
            with TestUtils.timeout_context(120):  # 2分钟超时
                success = ocr_engine.initialize()
            
            end_time = time.time()
            init_time = end_time - start_time
            
            if success:
                print(f"✅ OCR引擎初始化成功 (耗时: {init_time:.1f}s)")
                print(f"  - 进度更新次数: {len(progress_updates)}")
                print(f"  - 通用OCR: {'可用' if ocr_engine.ocr_general else '不可用'}")
                print(f"  - 表格OCR: {'可用' if ocr_engine.ocr_table else '不可用'}")
                return True
            else:
                print(f"❌ OCR引擎初始化失败 (耗时: {init_time:.1f}s)")
                if error_messages:
                    print(f"  错误信息: {error_messages[-1]}")
                return False
                
        except TimeoutError:
            print("❌ OCR引擎初始化超时")
            return False
        
    except Exception as e:
        print(f"❌ OCR引擎初始化异常: {e}")
        return False


def test_mode_switching() -> bool:
    """测试模式切换"""
    print("测试OCR模式切换...")
    
    try:
        from core.ocr_engine import OCREngine
        from config.settings import AppSettings
        from config.models_config import ModelsConfig
        
        # 创建并初始化OCR引擎
        settings = AppSettings()
        models_config = ModelsConfig()
        ocr_engine = OCREngine(settings.ocr, models_config)
        
        # 测试模式切换（不需要完全初始化）
        print("测试通用模式...")
        ocr_engine.set_mode('general')
        if ocr_engine.current_mode == 'general':
            print("✅ 切换到通用模式成功")
        else:
            print("❌ 切换到通用模式失败")
            return False
        
        print("测试表格模式...")
        ocr_engine.set_mode('table')
        if ocr_engine.current_mode == 'table':
            print("✅ 切换到表格模式成功")
        else:
            print("❌ 切换到表格模式失败")
            return False
        
        # 测试无效模式
        print("测试无效模式...")
        try:
            ocr_engine.set_mode('invalid_mode')
            print("❌ 应该拒绝无效模式")
            return False
        except ValueError:
            print("✅ 正确拒绝无效模式")
        
        return True
        
    except Exception as e:
        print(f"❌ 模式切换测试异常: {e}")
        return False


def test_paddleocr_availability() -> bool:
    """测试PaddleOCR可用性"""
    print("测试PaddleOCR可用性...")
    
    try:
        import paddleocr
        from paddleocr import PaddleOCR
        
        print("✅ PaddleOCR模块导入成功")
        print(f"  - PaddleOCR版本: {paddleocr.__version__ if hasattr(paddleocr, '__version__') else '未知'}")
        
        # 尝试创建PaddleOCR实例（使用最小配置）
        try:
            print("尝试创建PaddleOCR实例...")
            ocr = PaddleOCR(
                use_angle_cls=False,
                use_gpu=False,
                lang='ch',
                show_log=False
            )
            print("✅ PaddleOCR实例创建成功")
            return True
            
        except Exception as e:
            print(f"⚠️  PaddleOCR实例创建失败: {e}")
            print("  这可能是由于模型文件缺失，首次运行时会自动下载")
            return True  # 仍然认为测试通过，因为模块可用
        
    except ImportError as e:
        print(f"❌ PaddleOCR不可用: {e}")
        return False
    except Exception as e:
        print(f"❌ PaddleOCR测试异常: {e}")
        return False


def main():
    """主测试函数"""
    print("OCR引擎核心功能测试")
    print("=" * 50)
    
    # 创建测试运行器
    runner = OCRTestRunner()
    runner.setup()
    
    try:
        # 定义测试用例
        test_cases = [
            (test_dependencies_available, "依赖包可用性"),
            (test_ocr_engine_import, "OCR引擎模块导入"),
            (test_configuration_loading, "配置系统加载"),
            (test_ocr_engine_creation, "OCR引擎创建"),
            (test_mode_switching, "模式切换功能"),
            (test_paddleocr_availability, "PaddleOCR可用性"),
            (test_ocr_engine_initialization, "OCR引擎初始化"),
        ]
        
        # 运行测试
        for test_func, test_name in test_cases:
            print(f"\n{'='*20} {test_name} {'='*20}")
            runner.run_test(test_func, test_name)
        
        # 生成报告
        print("\n" + "="*60)
        print(runner.generate_report())
        
        # 保存报告
        runner.save_report("ocr_engine_core_test_report.txt")
        
        # 返回结果
        passed = sum(1 for r in runner.results if r['success'])
        total = len(runner.results)
        
        if passed == total:
            print(f"\n🎉 所有测试通过！OCR引擎核心功能正常。")
            return 0
        else:
            print(f"\n⚠️  {passed}/{total} 测试通过，请检查失败的测试。")
            return 1
    
    finally:
        runner.cleanup()


if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程发生未知错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
