#!/usr/bin/env python3
"""
图像处理功能测试
测试图像加载、预处理、格式转换等功能
"""

import sys
import os
import numpy as np
from pathlib import Path
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from test_utils import TestUtils, OCRTestRunner


def test_image_utils_import() -> bool:
    """测试图像工具模块导入"""
    print("测试图像工具模块导入...")
    
    try:
        from utils.image_utils import ImageUtils
        print("✅ 图像工具模块导入成功")
        return True
        
    except ImportError as e:
        print(f"❌ 图像工具模块导入失败: {e}")
        return False


def test_image_dependencies() -> bool:
    """测试图像处理依赖"""
    print("测试图像处理依赖...")
    
    dependencies = [
        ("OpenCV", "cv2"),
        ("PIL", "PIL"),
        ("NumPy", "numpy"),
        ("pdf2image", "pdf2image"),
    ]
    
    missing_deps = []
    
    for name, module in dependencies:
        try:
            __import__(module)
            print(f"✅ {name} 可用")
        except ImportError:
            print(f"❌ {name} 不可用")
            missing_deps.append(name)
    
    return len(missing_deps) == 0


def test_image_loading() -> bool:
    """测试图像加载功能"""
    print("测试图像加载...")
    
    try:
        from utils.image_utils import ImageUtils
        
        # 获取测试文件
        test_files = TestUtils.get_test_files()
        
        if 'png' not in test_files:
            print("⚠️  测试图片文件不存在，跳过图像加载测试")
            return True
        
        image_path = test_files['png']
        print(f"使用测试文件: {image_path.name}")
        
        # 测试OpenCV加载
        try:
            cv_image = ImageUtils.load_image(image_path)
            print(f"✅ OpenCV加载成功，尺寸: {cv_image.shape}")
            
            # 验证图像数据
            if cv_image is not None and cv_image.size > 0:
                print(f"  - 图像类型: {cv_image.dtype}")
                print(f"  - 图像形状: {cv_image.shape}")
            else:
                print("❌ 加载的图像数据无效")
                return False
                
        except Exception as e:
            print(f"❌ OpenCV加载失败: {e}")
            return False
        
        # 测试PIL加载
        try:
            pil_image = ImageUtils.load_image_pil(image_path)
            print(f"✅ PIL加载成功，尺寸: {pil_image.size}")
            
            # 验证图像数据
            if pil_image is not None:
                print(f"  - 图像模式: {pil_image.mode}")
                print(f"  - 图像格式: {pil_image.format}")
            else:
                print("❌ 加载的PIL图像无效")
                return False
                
        except Exception as e:
            print(f"❌ PIL加载失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 图像加载测试异常: {e}")
        return False


def test_image_format_conversion() -> bool:
    """测试图像格式转换"""
    print("测试图像格式转换...")
    
    try:
        from utils.image_utils import ImageUtils
        
        # 获取测试文件
        test_files = TestUtils.get_test_files()
        
        if 'png' not in test_files:
            print("⚠️  测试图片文件不存在，跳过格式转换测试")
            return True
        
        image_path = test_files['png']
        
        # 加载图像
        pil_image = ImageUtils.load_image_pil(image_path)
        cv_image = ImageUtils.load_image(image_path)
        
        # 测试PIL到OpenCV转换
        try:
            converted_cv = ImageUtils.pil_to_cv2(pil_image)
            print(f"✅ PIL到OpenCV转换成功，尺寸: {converted_cv.shape}")
            
            # 验证转换结果
            if converted_cv.shape[:2] != (pil_image.height, pil_image.width):
                print("❌ 转换后尺寸不匹配")
                return False
                
        except Exception as e:
            print(f"❌ PIL到OpenCV转换失败: {e}")
            return False
        
        # 测试OpenCV到PIL转换
        try:
            converted_pil = ImageUtils.cv2_to_pil(cv_image)
            print(f"✅ OpenCV到PIL转换成功，尺寸: {converted_pil.size}")
            
            # 验证转换结果
            if converted_pil.size != (cv_image.shape[1], cv_image.shape[0]):
                print("❌ 转换后尺寸不匹配")
                return False
                
        except Exception as e:
            print(f"❌ OpenCV到PIL转换失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 图像格式转换测试异常: {e}")
        return False


def test_image_preprocessing() -> bool:
    """测试图像预处理"""
    print("测试图像预处理...")
    
    try:
        from utils.image_utils import ImageUtils
        
        # 获取测试文件
        test_files = TestUtils.get_test_files()
        
        if 'png' not in test_files:
            print("⚠️  测试图片文件不存在，跳过预处理测试")
            return True
        
        image_path = test_files['png']
        
        # 加载图像
        cv_image = ImageUtils.load_image(image_path)
        original_shape = cv_image.shape
        
        # 测试OCR优化
        try:
            enhanced_image = ImageUtils.enhance_image_for_ocr(cv_image)
            print(f"✅ OCR优化成功，尺寸: {enhanced_image.shape}")
            
            # 验证处理结果
            if enhanced_image.shape[:2] != original_shape[:2]:
                print("❌ 优化后尺寸改变")
                return False
            
            # 检查是否转换为灰度图
            if len(enhanced_image.shape) == 2:
                print("  - 已转换为灰度图")
            elif enhanced_image.shape[2] == 1:
                print("  - 已转换为单通道图像")
            else:
                print("  - 保持多通道图像")
                
        except Exception as e:
            print(f"❌ OCR优化失败: {e}")
            return False
        
        # 测试图像调整大小
        try:
            resized_image = ImageUtils.resize_image(cv_image, width=800, height=600)
            print(f"✅ 图像调整大小成功，新尺寸: {resized_image.shape}")
            
            # 验证调整结果
            if resized_image.shape[1] > 800 or resized_image.shape[0] > 600:
                print("❌ 调整大小失败，尺寸超出预期")
                return False
                
        except Exception as e:
            print(f"❌ 图像调整大小失败: {e}")
            return False
        
        # 测试对比度增强
        try:
            enhanced_contrast = ImageUtils.enhance_contrast(cv_image, factor=1.5)
            print(f"✅ 对比度增强成功，尺寸: {enhanced_contrast.shape}")
            
            # 验证增强结果
            if enhanced_contrast.shape != cv_image.shape:
                print("❌ 对比度增强后尺寸改变")
                return False
                
        except Exception as e:
            print(f"❌ 对比度增强失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 图像预处理测试异常: {e}")
        return False


def test_image_operations() -> bool:
    """测试图像操作功能"""
    print("测试图像操作...")
    
    try:
        from utils.image_utils import ImageUtils
        
        # 获取测试文件
        test_files = TestUtils.get_test_files()
        
        if 'png' not in test_files:
            print("⚠️  测试图片文件不存在，跳过图像操作测试")
            return True
        
        image_path = test_files['png']
        
        # 加载图像
        cv_image = ImageUtils.load_image(image_path)
        
        # 测试图像旋转
        try:
            rotated_image = ImageUtils.rotate_image(cv_image, 45)
            print(f"✅ 图像旋转成功，新尺寸: {rotated_image.shape}")
            
            # 验证旋转结果
            if rotated_image.size == 0:
                print("❌ 旋转后图像为空")
                return False
                
        except Exception as e:
            print(f"❌ 图像旋转失败: {e}")
            return False
        
        # 测试图像裁剪
        try:
            h, w = cv_image.shape[:2]
            crop_x, crop_y = w // 4, h // 4
            crop_w, crop_h = w // 2, h // 2
            
            cropped_image = ImageUtils.crop_image(cv_image, crop_x, crop_y, crop_w, crop_h)
            print(f"✅ 图像裁剪成功，新尺寸: {cropped_image.shape}")
            
            # 验证裁剪结果
            if cropped_image.shape[0] != crop_h or cropped_image.shape[1] != crop_w:
                print("❌ 裁剪尺寸不正确")
                return False
                
        except Exception as e:
            print(f"❌ 图像裁剪失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 图像操作测试异常: {e}")
        return False


def test_image_info() -> bool:
    """测试图像信息获取"""
    print("测试图像信息获取...")
    
    try:
        from utils.image_utils import ImageUtils
        
        # 获取测试文件
        test_files = TestUtils.get_test_files()
        
        if 'png' not in test_files:
            print("⚠️  测试图片文件不存在，跳过图像信息测试")
            return True
        
        image_path = test_files['png']
        
        # 获取图像信息
        try:
            image_info = ImageUtils.get_image_info(image_path)
            print(f"✅ 图像信息获取成功:")
            print(f"  - 宽度: {image_info['width']}")
            print(f"  - 高度: {image_info['height']}")
            print(f"  - 模式: {image_info['mode']}")
            print(f"  - 格式: {image_info['format']}")
            print(f"  - 文件大小: {image_info['size_bytes']} 字节")
            
            # 验证信息完整性
            required_keys = ['width', 'height', 'mode', 'format', 'size_bytes']
            for key in required_keys:
                if key not in image_info:
                    print(f"❌ 缺少图像信息: {key}")
                    return False
            
            return True
            
        except Exception as e:
            print(f"❌ 图像信息获取失败: {e}")
            return False
        
    except Exception as e:
        print(f"❌ 图像信息测试异常: {e}")
        return False


def main():
    """主测试函数"""
    print("图像处理功能测试")
    print("=" * 50)
    
    # 创建测试运行器
    runner = OCRTestRunner()
    runner.setup()
    
    try:
        # 定义测试用例
        test_cases = [
            (test_image_utils_import, "图像工具模块导入"),
            (test_image_dependencies, "图像处理依赖"),
            (test_image_loading, "图像加载功能"),
            (test_image_format_conversion, "图像格式转换"),
            (test_image_preprocessing, "图像预处理"),
            (test_image_operations, "图像操作功能"),
            (test_image_info, "图像信息获取"),
        ]
        
        # 运行测试
        for test_func, test_name in test_cases:
            print(f"\n{'='*20} {test_name} {'='*20}")
            runner.run_test(test_func, test_name)
        
        # 生成报告
        print("\n" + "="*60)
        print(runner.generate_report())
        
        # 保存报告
        runner.save_report("image_processing_test_report.txt")
        
        # 返回结果
        passed = sum(1 for r in runner.results if r['success'])
        total = len(runner.results)
        
        if passed == total:
            print(f"\n🎉 所有测试通过！图像处理功能正常。")
            return 0
        else:
            print(f"\n⚠️  {passed}/{total} 测试通过，请检查失败的测试。")
            return 1
    
    finally:
        runner.cleanup()


if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程发生未知错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
