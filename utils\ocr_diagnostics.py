"""
OCR诊断和故障排除工具
"""

import os
import cv2
import numpy as np
import logging
import time
from typing import Dict, List, Tuple, Optional, Union
from pathlib import Path
from PIL import Image

from core.ocr_engine import OCREngine, OCRResult
from config.settings import AppSettings
from config.models_config import ModelsConfig
from utils.image_utils import ImageUtils


class OCRDiagnostics:
    """OCR诊断工具类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def diagnose_image_quality(self, image_path: Union[str, Path]) -> Dict[str, any]:
        """
        诊断图像质量
        
        Args:
            image_path: 图像路径
            
        Returns:
            诊断结果字典
        """
        try:
            # 获取基本图像信息
            img_info = ImageUtils.get_image_info(image_path)
            
            # 加载图像进行分析
            image = ImageUtils.load_image(image_path)
            
            # 转换为灰度图分析
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image
            
            # 计算图像质量指标
            mean_brightness = np.mean(gray)
            std_brightness = np.std(gray)
            contrast_ratio = std_brightness / mean_brightness if mean_brightness > 0 else 0
            
            # 检测图像清晰度（使用拉普拉斯算子）
            laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
            
            # 检测文本区域密度
            edges = cv2.Canny(gray, 50, 150)
            text_density = np.sum(edges > 0) / (edges.shape[0] * edges.shape[1])
            
            diagnosis = {
                'basic_info': img_info,
                'brightness': {
                    'mean': mean_brightness,
                    'std': std_brightness,
                    'status': self._evaluate_brightness(mean_brightness)
                },
                'contrast': {
                    'ratio': contrast_ratio,
                    'status': self._evaluate_contrast(contrast_ratio)
                },
                'sharpness': {
                    'laplacian_variance': laplacian_var,
                    'status': self._evaluate_sharpness(laplacian_var)
                },
                'text_density': {
                    'value': text_density,
                    'status': self._evaluate_text_density(text_density)
                },
                'overall_quality': 'good'  # 将在后面计算
            }
            
            # 计算整体质量评估
            diagnosis['overall_quality'] = self._calculate_overall_quality(diagnosis)
            
            return diagnosis
            
        except Exception as e:
            self.logger.error(f"图像质量诊断失败: {e}")
            return {'error': str(e)}
    
    def _evaluate_brightness(self, brightness: float) -> str:
        """评估亮度状态"""
        if brightness < 30:
            return 'too_dark'
        elif brightness > 220:
            return 'too_bright'
        elif brightness < 80:
            return 'dark'
        elif brightness > 180:
            return 'bright'
        else:
            return 'good'
    
    def _evaluate_contrast(self, contrast: float) -> str:
        """评估对比度状态"""
        if contrast < 0.1:
            return 'too_low'
        elif contrast < 0.3:
            return 'low'
        elif contrast > 1.0:
            return 'high'
        else:
            return 'good'
    
    def _evaluate_sharpness(self, laplacian_var: float) -> str:
        """评估清晰度状态"""
        if laplacian_var < 50:
            return 'blurry'
        elif laplacian_var < 100:
            return 'slightly_blurry'
        else:
            return 'sharp'
    
    def _evaluate_text_density(self, density: float) -> str:
        """评估文本密度状态"""
        if density < 0.01:
            return 'very_low'
        elif density < 0.05:
            return 'low'
        elif density > 0.3:
            return 'high'
        else:
            return 'good'
    
    def _calculate_overall_quality(self, diagnosis: Dict) -> str:
        """计算整体质量评估"""
        issues = []
        
        if diagnosis['brightness']['status'] in ['too_dark', 'too_bright']:
            issues.append('brightness')
        if diagnosis['contrast']['status'] in ['too_low']:
            issues.append('contrast')
        if diagnosis['sharpness']['status'] in ['blurry']:
            issues.append('sharpness')
        if diagnosis['text_density']['status'] in ['very_low']:
            issues.append('text_density')
        
        if len(issues) == 0:
            return 'excellent'
        elif len(issues) == 1:
            return 'good'
        elif len(issues) == 2:
            return 'fair'
        else:
            return 'poor'
    
    def test_ocr_parameters(self, image_path: Union[str, Path]) -> Dict[str, any]:
        """
        测试不同OCR参数的效果
        
        Args:
            image_path: 图像路径
            
        Returns:
            测试结果字典
        """
        try:
            results = {}
            
            # 测试参数组合
            test_configs = [
                {
                    'name': 'default',
                    'det_db_thresh': 0.3,
                    'det_db_box_thresh': 0.6,
                    'rec_batch_num': 6
                },
                {
                    'name': 'sensitive',
                    'det_db_thresh': 0.1,
                    'det_db_box_thresh': 0.3,
                    'rec_batch_num': 6
                },
                {
                    'name': 'strict',
                    'det_db_thresh': 0.5,
                    'det_db_box_thresh': 0.8,
                    'rec_batch_num': 6
                }
            ]
            
            for config in test_configs:
                try:
                    start_time = time.time()
                    
                    # 创建临时OCR引擎
                    settings = AppSettings()
                    models_config = ModelsConfig()
                    ocr_engine = OCREngine(settings.ocr, models_config)
                    
                    # 修改参数（这里需要重新初始化OCR实例）
                    ocr_kwargs = {
                        'use_angle_cls': settings.ocr.use_angle_cls,
                        'lang': settings.ocr.lang,
                        'det_db_thresh': config['det_db_thresh'],
                        'det_db_box_thresh': config['det_db_box_thresh'],
                        'rec_batch_num': config['rec_batch_num']
                    }
                    
                    from paddleocr import PaddleOCR
                    temp_ocr = PaddleOCR(**ocr_kwargs)
                    
                    # 加载和处理图像
                    image = ImageUtils.load_image(image_path)
                    ocr_results = temp_ocr.predict(image)
                    
                    processing_time = time.time() - start_time
                    
                    # 分析结果
                    text_blocks = 0
                    total_confidence = 0
                    if ocr_results and ocr_results[0]:
                        for line in ocr_results[0]:
                            if len(line) >= 2 and isinstance(line[1], (list, tuple)) and len(line[1]) >= 2:
                                text_blocks += 1
                                total_confidence += line[1][1]
                    
                    avg_confidence = total_confidence / text_blocks if text_blocks > 0 else 0
                    
                    results[config['name']] = {
                        'config': config,
                        'text_blocks': text_blocks,
                        'avg_confidence': avg_confidence,
                        'processing_time': processing_time,
                        'success': text_blocks > 0
                    }
                    
                except Exception as e:
                    results[config['name']] = {
                        'config': config,
                        'error': str(e),
                        'success': False
                    }
            
            return results
            
        except Exception as e:
            self.logger.error(f"OCR参数测试失败: {e}")
            return {'error': str(e)}
    
    def generate_diagnostic_report(self, image_path: Union[str, Path], 
                                 output_dir: Optional[Union[str, Path]] = None) -> str:
        """
        生成完整的诊断报告
        
        Args:
            image_path: 图像路径
            output_dir: 输出目录
            
        Returns:
            报告文件路径
        """
        try:
            if output_dir is None:
                output_dir = Path(image_path).parent / "ocr_diagnostics"
            
            output_dir = Path(output_dir)
            output_dir.mkdir(exist_ok=True)
            
            # 执行诊断
            image_diagnosis = self.diagnose_image_quality(image_path)
            parameter_test = self.test_ocr_parameters(image_path)
            
            # 生成报告
            report_path = output_dir / f"diagnostic_report_{int(time.time())}.txt"
            
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write("OCR诊断报告\n")
                f.write("=" * 50 + "\n")
                f.write(f"图像文件: {image_path}\n")
                f.write(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                # 图像质量诊断
                f.write("图像质量诊断:\n")
                f.write("-" * 30 + "\n")
                if 'error' not in image_diagnosis:
                    f.write(f"整体质量: {image_diagnosis['overall_quality']}\n")
                    f.write(f"图像尺寸: {image_diagnosis['basic_info']['width']}x{image_diagnosis['basic_info']['height']}\n")
                    f.write(f"亮度状态: {image_diagnosis['brightness']['status']} (均值: {image_diagnosis['brightness']['mean']:.1f})\n")
                    f.write(f"对比度状态: {image_diagnosis['contrast']['status']} (比值: {image_diagnosis['contrast']['ratio']:.3f})\n")
                    f.write(f"清晰度状态: {image_diagnosis['sharpness']['status']} (方差: {image_diagnosis['sharpness']['laplacian_variance']:.1f})\n")
                    f.write(f"文本密度状态: {image_diagnosis['text_density']['status']} (密度: {image_diagnosis['text_density']['value']:.3f})\n")
                else:
                    f.write(f"诊断失败: {image_diagnosis['error']}\n")
                
                f.write("\n")
                
                # OCR参数测试结果
                f.write("OCR参数测试结果:\n")
                f.write("-" * 30 + "\n")
                if 'error' not in parameter_test:
                    for name, result in parameter_test.items():
                        f.write(f"\n{name.upper()} 配置:\n")
                        if result['success']:
                            f.write(f"  检测文本块: {result['text_blocks']}\n")
                            f.write(f"  平均置信度: {result['avg_confidence']:.3f}\n")
                            f.write(f"  处理时间: {result['processing_time']:.2f}s\n")
                        else:
                            f.write(f"  失败: {result.get('error', '未知错误')}\n")
                else:
                    f.write(f"测试失败: {parameter_test['error']}\n")
                
                # 建议
                f.write("\n建议:\n")
                f.write("-" * 30 + "\n")
                suggestions = self._generate_suggestions(image_diagnosis, parameter_test)
                for suggestion in suggestions:
                    f.write(f"- {suggestion}\n")
            
            return str(report_path)
            
        except Exception as e:
            self.logger.error(f"生成诊断报告失败: {e}")
            return ""
    
    def _generate_suggestions(self, image_diagnosis: Dict, parameter_test: Dict) -> List[str]:
        """生成改进建议"""
        suggestions = []
        
        if 'error' not in image_diagnosis:
            # 基于图像质量的建议
            if image_diagnosis['brightness']['status'] in ['too_dark', 'dark']:
                suggestions.append("图像过暗，建议增加亮度或使用图像增强")
            elif image_diagnosis['brightness']['status'] in ['too_bright', 'bright']:
                suggestions.append("图像过亮，建议降低亮度或调整曝光")
            
            if image_diagnosis['contrast']['status'] == 'too_low':
                suggestions.append("对比度过低，建议增强对比度")
            
            if image_diagnosis['sharpness']['status'] == 'blurry':
                suggestions.append("图像模糊，建议使用更高分辨率的图像或锐化处理")
            
            if image_diagnosis['text_density']['status'] == 'very_low':
                suggestions.append("文本密度很低，可能不包含文本内容")
        
        if 'error' not in parameter_test:
            # 基于参数测试的建议
            best_config = None
            best_score = 0
            
            for name, result in parameter_test.items():
                if result['success']:
                    score = result['text_blocks'] * result['avg_confidence']
                    if score > best_score:
                        best_score = score
                        best_config = name
            
            if best_config:
                suggestions.append(f"推荐使用 {best_config} 参数配置以获得最佳效果")
            else:
                suggestions.append("所有参数配置都未能识别到文本，建议检查图像质量")
        
        if not suggestions:
            suggestions.append("未发现明显问题，如果仍无法识别文本，请检查图像是否包含可识别的文本内容")
        
        return suggestions
