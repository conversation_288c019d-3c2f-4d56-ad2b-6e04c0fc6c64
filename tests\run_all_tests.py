#!/usr/bin/env python3
"""
运行所有测试的主脚本
"""

import sys
import os
import subprocess
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def run_test_script(script_path, description):
    """运行单个测试脚本"""
    print(f"\n{'='*60}")
    print(f"运行测试: {description}")
    print(f"脚本: {script_path}")
    print('='*60)
    
    try:
        start_time = time.time()
        result = subprocess.run([
            sys.executable, str(script_path)
        ], capture_output=False, text=True, cwd=project_root)
        
        end_time = time.time()
        duration = end_time - start_time
        
        if result.returncode == 0:
            print(f"\n✅ {description} - 通过 (耗时: {duration:.2f}秒)")
            return True
        else:
            print(f"\n❌ {description} - 失败 (耗时: {duration:.2f}秒)")
            return False
            
    except Exception as e:
        print(f"\n❌ {description} - 异常: {e}")
        return False


def main():
    """主函数"""
    print("Prisma OCR & Translator - 完整测试套件")
    print("=" * 60)
    print(f"项目根目录: {project_root}")
    print(f"Python版本: {sys.version}")
    
    # 定义测试脚本列表
    test_scripts = [
        # 基础测试
        ("tests/test_dependencies.py", "依赖包检查"),
        ("tests/test_summary.py", "环境检查总结"),
        ("tests/test_config.py", "配置系统"),
        ("tests/test_webui.py", "Web UI功能"),

        # OCR测试（可能需要较长时间）
        ("tests/test_pdf_processing.py", "PDF处理功能"),
    ]
    
    # 可选的高级测试（需要完整依赖）
    advanced_tests = [
        ("tests/process_pdf.py", "完整PDF处理"),
    ]
    
    print(f"\n计划运行 {len(test_scripts)} 个基础测试")
    
    # 询问是否运行高级测试
    try:
        response = input("\n是否运行高级测试（需要PaddleOCR等完整依赖）？ [y/N]: ").strip().lower()
        if response in ['y', 'yes', '是']:
            test_scripts.extend(advanced_tests)
            print(f"将运行 {len(test_scripts)} 个测试（包括高级测试）")
        else:
            print(f"将运行 {len(test_scripts)} 个基础测试")
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        return 1
    
    # 运行测试
    passed_tests = []
    failed_tests = []
    total_start_time = time.time()
    
    for script_path, description in test_scripts:
        full_script_path = project_root / script_path
        
        if not full_script_path.exists():
            print(f"\n⚠️  跳过测试: {description} (脚本不存在: {script_path})")
            continue
        
        if run_test_script(full_script_path, description):
            passed_tests.append(description)
        else:
            failed_tests.append(description)
    
    total_end_time = time.time()
    total_duration = total_end_time - total_start_time
    
    # 输出总结
    print("\n" + "="*60)
    print("测试总结")
    print("="*60)
    print(f"总耗时: {total_duration:.2f}秒")
    print(f"总测试数: {len(passed_tests) + len(failed_tests)}")
    print(f"通过: {len(passed_tests)}")
    print(f"失败: {len(failed_tests)}")
    
    if passed_tests:
        print(f"\n✅ 通过的测试:")
        for test in passed_tests:
            print(f"  - {test}")
    
    if failed_tests:
        print(f"\n❌ 失败的测试:")
        for test in failed_tests:
            print(f"  - {test}")
        
        print(f"\n建议:")
        print("1. 检查是否安装了所有必需的依赖包")
        print("2. 运行 'python tests/test_dependencies.py' 检查环境")
        print("3. 查看具体的错误信息并逐个解决")
    
    # 最终结果
    if len(failed_tests) == 0:
        print(f"\n🎉 所有测试通过！项目环境配置正常。")
        return 0
    elif len(passed_tests) > len(failed_tests):
        print(f"\n⚠️  大部分测试通过，但仍有问题需要解决。")
        return 1
    else:
        print(f"\n❌ 多数测试失败，请检查环境配置。")
        return 2


if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n测试套件被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试套件运行异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
