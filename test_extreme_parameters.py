#!/usr/bin/env python3
"""
测试极端参数设置
"""

from core.ocr_engine import OCREngine
from config.settings import AppSettings
from config.models_config import ModelsConfig
from utils.image_utils import ImageUtils
import time

def test_extreme_parameters():
    print("=== 测试极端参数设置 ===")
    
    # 测试图像
    test_image = 'ocr测试.PNG'
    
    # 加载图像
    print(f"正在加载图像: {test_image}")
    image = ImageUtils.load_image_pil(test_image)
    
    # 极端参数配置
    extreme_configs = [
        {
            'name': '超敏感检测',
            'det_db_thresh': 0.01,
            'det_db_box_thresh': 0.1,
            'rec_batch_num': 1
        },
        {
            'name': '最低阈值',
            'det_db_thresh': 0.001,
            'det_db_box_thresh': 0.01,
            'rec_batch_num': 1
        },
        {
            'name': '无阈值限制',
            'det_db_thresh': 0.0,
            'det_db_box_thresh': 0.0,
            'rec_batch_num': 1
        }
    ]
    
    for config in extreme_configs:
        print(f"\n测试配置: {config['name']}")
        print(f"  det_db_thresh: {config['det_db_thresh']}")
        print(f"  det_db_box_thresh: {config['det_db_box_thresh']}")
        print(f"  rec_batch_num: {config['rec_batch_num']}")
        
        try:
            # 创建临时OCR引擎
            settings = AppSettings()
            models_config = ModelsConfig()
            ocr_engine = OCREngine(settings.ocr, models_config)
            
            # 手动设置极端参数
            print("  正在初始化OCR引擎...")
            
            # 直接创建PaddleOCR实例，绕过我们的封装
            from paddleocr import PaddleOCR
            
            ocr_kwargs = {
                'use_angle_cls': True,
                'lang': 'ch',
                'det_db_thresh': config['det_db_thresh'],
                'det_db_box_thresh': config['det_db_box_thresh'],
                'rec_batch_num': config['rec_batch_num'],
                'show_log': False
            }
            
            temp_ocr = PaddleOCR(**ocr_kwargs)
            
            # 直接调用OCR
            start_time = time.time()
            
            # 转换PIL图像为numpy数组
            import numpy as np
            img_array = np.array(image)
            
            # 调用OCR
            result = temp_ocr.ocr(img_array, cls=True)
            end_time = time.time()
            
            print(f"  处理时间: {end_time - start_time:.2f}s")
            
            # 分析结果
            if result and result[0]:
                text_blocks = result[0]
                print(f"  识别到 {len(text_blocks)} 个文本块")
                
                for i, block in enumerate(text_blocks[:5]):
                    if len(block) >= 2:
                        text = block[1][0] if block[1] else ""
                        confidence = block[1][1] if len(block[1]) > 1 else 0.0
                        print(f"    {i+1}. {text} (置信度: {confidence:.3f})")
            else:
                print("  未识别到文本内容")
                
        except Exception as e:
            print(f"  配置测试失败: {e}")
    
    # 测试不同语言设置
    print(f"\n=== 测试不同语言设置 ===")
    
    language_configs = ['ch', 'en', 'chinese_cht']
    
    for lang in language_configs:
        print(f"\n测试语言: {lang}")
        
        try:
            from paddleocr import PaddleOCR
            
            ocr_kwargs = {
                'use_angle_cls': True,
                'lang': lang,
                'det_db_thresh': 0.1,
                'det_db_box_thresh': 0.3,
                'rec_batch_num': 1,
                'show_log': False
            }
            
            temp_ocr = PaddleOCR(**ocr_kwargs)
            
            start_time = time.time()
            img_array = np.array(image)
            result = temp_ocr.ocr(img_array, cls=True)
            end_time = time.time()
            
            print(f"  处理时间: {end_time - start_time:.2f}s")
            
            if result and result[0]:
                text_blocks = result[0]
                print(f"  识别到 {len(text_blocks)} 个文本块")
                
                for i, block in enumerate(text_blocks[:3]):
                    if len(block) >= 2:
                        text = block[1][0] if block[1] else ""
                        confidence = block[1][1] if len(block[1]) > 1 else 0.0
                        print(f"    {i+1}. {text} (置信度: {confidence:.3f})")
            else:
                print("  未识别到文本内容")
                
        except Exception as e:
            print(f"  语言测试失败: {e}")
    
    # 测试原始PaddleOCR默认参数
    print(f"\n=== 测试PaddleOCR默认参数 ===")
    
    try:
        from paddleocr import PaddleOCR
        
        # 使用最简单的默认配置
        default_ocr = PaddleOCR(use_angle_cls=True, lang='ch', show_log=False)
        
        start_time = time.time()
        img_array = np.array(image)
        result = default_ocr.ocr(img_array, cls=True)
        end_time = time.time()
        
        print(f"处理时间: {end_time - start_time:.2f}s")
        
        if result and result[0]:
            text_blocks = result[0]
            print(f"识别到 {len(text_blocks)} 个文本块")
            
            for i, block in enumerate(text_blocks[:5]):
                if len(block) >= 2:
                    text = block[1][0] if block[1] else ""
                    confidence = block[1][1] if len(block[1]) > 1 else 0.0
                    print(f"  {i+1}. {text} (置信度: {confidence:.3f})")
        else:
            print("未识别到文本内容")
            
    except Exception as e:
        print(f"默认参数测试失败: {e}")

if __name__ == "__main__":
    test_extreme_parameters()
