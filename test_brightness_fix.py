#!/usr/bin/env python3
"""
测试亮度修复功能
"""

from core.ocr_engine import OCREngine
from config.settings import AppSettings
from config.models_config import ModelsConfig
from utils.image_utils import ImageUtils
import cv2
import numpy as np
import time

def test_brightness_correction():
    print("=== 测试亮度修复功能 ===")
    
    # 测试图像
    test_image = 'ocr测试.PNG'
    
    # 初始化OCR引擎
    settings = AppSettings()
    models_config = ModelsConfig()
    ocr_engine = OCREngine(settings.ocr, models_config)
    
    print("正在初始化OCR引擎...")
    if not ocr_engine.initialize():
        print("OCR引擎初始化失败")
        return
    
    # 加载原始图像
    print(f"正在加载图像: {test_image}")
    original_image = ImageUtils.load_image(test_image)
    
    # 分析原始图像亮度
    gray = cv2.cvtColor(original_image, cv2.COLOR_BGR2GRAY)
    original_brightness = np.mean(gray)
    print(f"原始图像平均亮度: {original_brightness:.1f}")
    
    # 测试1: 原始图像OCR
    print("\n1. 测试原始图像OCR:")
    pil_image = ImageUtils.cv2_to_pil(original_image)
    start_time = time.time()
    result1 = ocr_engine.process_image(pil_image, page_number=1)
    end_time = time.time()
    
    print(f"   处理时间: {end_time - start_time:.2f}s")
    print(f"   识别文本块数: {len(result1.text_blocks)}")
    
    # 测试2: 手动亮度调整
    print("\n2. 测试手动亮度调整:")
    
    # 降低亮度
    brightness_factor = 0.7  # 降低30%的亮度
    adjusted_image = cv2.convertScaleAbs(original_image, alpha=brightness_factor, beta=0)
    
    adjusted_gray = cv2.cvtColor(adjusted_image, cv2.COLOR_BGR2GRAY)
    adjusted_brightness = np.mean(adjusted_gray)
    print(f"   调整后平均亮度: {adjusted_brightness:.1f}")
    
    # 保存调整后的图像用于调试
    cv2.imwrite("debug_brightness_adjusted.jpg", adjusted_image)
    print("   已保存调整后的图像: debug_brightness_adjusted.jpg")
    
    # OCR处理调整后的图像
    pil_adjusted = ImageUtils.cv2_to_pil(adjusted_image)
    start_time = time.time()
    result2 = ocr_engine.process_image(pil_adjusted, page_number=1)
    end_time = time.time()
    
    print(f"   处理时间: {end_time - start_time:.2f}s")
    print(f"   识别文本块数: {len(result2.text_blocks)}")
    
    if result2.text_blocks:
        print("   识别结果:")
        for i, block in enumerate(result2.text_blocks[:5]):
            text = block.get('text', '').strip()
            confidence = block.get('confidence', 0.0)
            print(f"     {i+1}. {text} (置信度: {confidence:.3f})")
    
    # 测试3: 使用CLAHE (对比度限制自适应直方图均衡化)
    print("\n3. 测试CLAHE增强:")
    
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
    enhanced_gray = clahe.apply(gray)
    
    # 转换回彩色
    lab = cv2.cvtColor(original_image, cv2.COLOR_BGR2LAB)
    lab[:,:,0] = enhanced_gray
    enhanced_image = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
    
    enhanced_brightness = np.mean(enhanced_gray)
    print(f"   CLAHE增强后平均亮度: {enhanced_brightness:.1f}")
    
    # 保存增强后的图像
    cv2.imwrite("debug_clahe_enhanced.jpg", enhanced_image)
    print("   已保存CLAHE增强图像: debug_clahe_enhanced.jpg")
    
    # OCR处理增强后的图像
    pil_enhanced = ImageUtils.cv2_to_pil(enhanced_image)
    start_time = time.time()
    result3 = ocr_engine.process_image(pil_enhanced, page_number=1)
    end_time = time.time()
    
    print(f"   处理时间: {end_time - start_time:.2f}s")
    print(f"   识别文本块数: {len(result3.text_blocks)}")
    
    if result3.text_blocks:
        print("   识别结果:")
        for i, block in enumerate(result3.text_blocks[:5]):
            text = block.get('text', '').strip()
            confidence = block.get('confidence', 0.0)
            print(f"     {i+1}. {text} (置信度: {confidence:.3f})")
    
    # 测试4: 组合处理（亮度调整 + CLAHE）
    print("\n4. 测试组合处理:")
    
    # 先调整亮度，再应用CLAHE
    combo_image = cv2.convertScaleAbs(original_image, alpha=0.8, beta=0)
    combo_gray = cv2.cvtColor(combo_image, cv2.COLOR_BGR2GRAY)
    combo_enhanced = clahe.apply(combo_gray)
    
    # 转换回彩色
    combo_lab = cv2.cvtColor(combo_image, cv2.COLOR_BGR2LAB)
    combo_lab[:,:,0] = combo_enhanced
    combo_final = cv2.cvtColor(combo_lab, cv2.COLOR_LAB2BGR)
    
    combo_brightness = np.mean(combo_enhanced)
    print(f"   组合处理后平均亮度: {combo_brightness:.1f}")
    
    # 保存组合处理后的图像
    cv2.imwrite("debug_combo_enhanced.jpg", combo_final)
    print("   已保存组合处理图像: debug_combo_enhanced.jpg")
    
    # OCR处理组合后的图像
    pil_combo = ImageUtils.cv2_to_pil(combo_final)
    start_time = time.time()
    result4 = ocr_engine.process_image(pil_combo, page_number=1)
    end_time = time.time()
    
    print(f"   处理时间: {end_time - start_time:.2f}s")
    print(f"   识别文本块数: {len(result4.text_blocks)}")
    
    if result4.text_blocks:
        print("   识别结果:")
        for i, block in enumerate(result4.text_blocks[:5]):
            text = block.get('text', '').strip()
            confidence = block.get('confidence', 0.0)
            print(f"     {i+1}. {text} (置信度: {confidence:.3f})")
    
    # 总结
    print("\n=== 测试总结 ===")
    results = [
        ("原始图像", len(result1.text_blocks)),
        ("亮度调整", len(result2.text_blocks)),
        ("CLAHE增强", len(result3.text_blocks)),
        ("组合处理", len(result4.text_blocks))
    ]
    
    best_method = max(results, key=lambda x: x[1])
    
    for method, count in results:
        status = "✓" if count > 0 else "✗"
        print(f"   {status} {method}: {count} 个文本块")
    
    print(f"\n最佳方法: {best_method[0]} ({best_method[1]} 个文本块)")
    
    if best_method[1] > 0:
        print("✓ 找到了有效的图像处理方法！")
    else:
        print("✗ 所有方法都未能识别到文本，可能需要检查图像内容或OCR参数")

if __name__ == "__main__":
    test_brightness_correction()
