#!/usr/bin/env python3
"""
测试工具类
提供测试所需的通用功能和工具
"""

import os
import sys
import time
import logging
import tempfile
import shutil
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from contextlib import contextmanager
import psutil

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class TestUtils:
    """测试工具类"""
    
    @staticmethod
    def setup_test_environment():
        """设置测试环境"""
        # 设置日志级别
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 创建临时目录
        temp_dir = Path(tempfile.gettempdir()) / "prisma_ocr_tests"
        temp_dir.mkdir(exist_ok=True)
        
        return temp_dir
    
    @staticmethod
    def cleanup_test_environment(temp_dir: Path):
        """清理测试环境"""
        if temp_dir.exists():
            shutil.rmtree(temp_dir, ignore_errors=True)
    
    @staticmethod
    def get_test_files() -> Dict[str, Path]:
        """获取测试文件路径"""
        test_files = {}
        
        # 测试图片
        png_file = project_root / "ocr测试.PNG"
        if png_file.exists():
            test_files['png'] = png_file
        
        # 测试PDF
        pdf_file = project_root / "ocr测试.pdf"
        if pdf_file.exists():
            test_files['pdf'] = pdf_file
        
        return test_files
    
    @staticmethod
    def check_dependencies() -> Tuple[bool, List[str]]:
        """检查依赖包"""
        dependencies = [
            ("numpy", "numpy"),
            ("PIL", "PIL"),
            ("cv2", "cv2"),
            ("pdf2image", "pdf2image"),
            ("paddle", "paddle"),
            ("paddleocr", "paddleocr"),
            ("psutil", "psutil"),
        ]
        
        missing_deps = []
        
        for name, module in dependencies:
            try:
                __import__(module)
            except ImportError:
                missing_deps.append(name)
        
        return len(missing_deps) == 0, missing_deps
    
    @staticmethod
    def measure_performance(func, *args, **kwargs) -> Tuple[Any, Dict[str, float]]:
        """测量函数性能"""
        # 记录开始状态
        process = psutil.Process()
        start_memory = process.memory_info().rss / 1024 / 1024  # MB
        start_time = time.time()
        
        # 执行函数
        result = func(*args, **kwargs)
        
        # 记录结束状态
        end_time = time.time()
        end_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        performance_data = {
            'execution_time': end_time - start_time,
            'memory_start': start_memory,
            'memory_end': end_memory,
            'memory_delta': end_memory - start_memory
        }
        
        return result, performance_data
    
    @staticmethod
    def validate_ocr_result(result) -> Dict[str, Any]:
        """验证OCR结果"""
        validation = {
            'is_valid': False,
            'has_text': False,
            'text_count': 0,
            'average_confidence': 0.0,
            'errors': []
        }
        
        try:
            if result is None:
                validation['errors'].append("结果为空")
                return validation
            
            # 检查结果结构
            if hasattr(result, 'text_blocks'):
                validation['text_count'] = len(result.text_blocks)
                validation['has_text'] = validation['text_count'] > 0
                
                if validation['has_text']:
                    # 计算平均置信度
                    confidences = [block.get('confidence', 0.0) for block in result.text_blocks]
                    validation['average_confidence'] = sum(confidences) / len(confidences)
                
                validation['is_valid'] = True
            else:
                validation['errors'].append("结果格式不正确")
        
        except Exception as e:
            validation['errors'].append(f"验证过程出错: {e}")
        
        return validation
    
    @staticmethod
    @contextmanager
    def timeout_context(seconds: int):
        """超时上下文管理器"""
        import signal
        
        def timeout_handler(signum, frame):
            raise TimeoutError(f"操作超时 ({seconds}秒)")
        
        # 设置信号处理器
        old_handler = signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(seconds)
        
        try:
            yield
        finally:
            # 恢复原始处理器
            signal.alarm(0)
            signal.signal(signal.SIGALRM, old_handler)
    
    @staticmethod
    def format_test_result(test_name: str, success: bool,
                          duration: float = 0.0,
                          details: str = "") -> str:
        """格式化测试结果"""
        status = "[PASS]" if success else "[FAIL]"
        duration_str = f" ({duration:.2f}s)" if duration > 0 else ""
        details_str = f" - {details}" if details else ""

        return f"{status} {test_name}{duration_str}{details_str}"
    
    @staticmethod
    def create_test_report(results: List[Dict[str, Any]]) -> str:
        """创建测试报告"""
        total_tests = len(results)
        passed_tests = sum(1 for r in results if r.get('success', False))
        failed_tests = total_tests - passed_tests
        
        total_time = sum(r.get('duration', 0.0) for r in results)
        
        report = []
        report.append("=" * 60)
        report.append("OCR功能测试报告")
        report.append("=" * 60)
        report.append(f"总测试数: {total_tests}")
        report.append(f"通过: {passed_tests}")
        report.append(f"失败: {failed_tests}")
        report.append(f"总耗时: {total_time:.2f}秒")
        report.append(f"成功率: {(passed_tests/total_tests*100):.1f}%")
        report.append("")
        
        # 详细结果
        report.append("详细结果:")
        report.append("-" * 40)
        
        for result in results:
            name = result.get('name', '未知测试')
            success = result.get('success', False)
            duration = result.get('duration', 0.0)
            details = result.get('details', '')
            
            report.append(TestUtils.format_test_result(name, success, duration, details))
        
        # 性能统计
        if any('performance' in r for r in results):
            report.append("")
            report.append("性能统计:")
            report.append("-" * 40)
            
            for result in results:
                if 'performance' in result:
                    perf = result['performance']
                    name = result.get('name', '未知测试')
                    report.append(f"{name}:")
                    report.append(f"  执行时间: {perf.get('execution_time', 0):.2f}s")
                    report.append(f"  内存使用: {perf.get('memory_delta', 0):.1f}MB")
        
        return "\n".join(report)


class OCRTestRunner:
    """OCR测试运行器"""
    
    def __init__(self):
        self.results = []
        self.temp_dir = None
        self.logger = logging.getLogger(__name__)
    
    def setup(self):
        """设置测试环境"""
        self.temp_dir = TestUtils.setup_test_environment()
        self.logger.info(f"测试环境已设置: {self.temp_dir}")
    
    def cleanup(self):
        """清理测试环境"""
        if self.temp_dir:
            TestUtils.cleanup_test_environment(self.temp_dir)
            self.logger.info("测试环境已清理")
    
    def run_test(self, test_func, test_name: str, *args, **kwargs) -> Dict[str, Any]:
        """运行单个测试"""
        self.logger.info(f"开始测试: {test_name}")
        
        start_time = time.time()
        result = {
            'name': test_name,
            'success': False,
            'duration': 0.0,
            'details': '',
            'error': None
        }
        
        try:
            # 执行测试
            test_result, performance_data = TestUtils.measure_performance(
                test_func, *args, **kwargs
            )
            
            result['success'] = test_result
            result['performance'] = performance_data
            
            if test_result:
                result['details'] = '测试通过'
            else:
                result['details'] = '测试失败'
                
        except Exception as e:
            result['error'] = str(e)
            result['details'] = f'测试异常: {e}'
            self.logger.error(f"测试 {test_name} 异常: {e}")
        
        finally:
            result['duration'] = time.time() - start_time
        
        self.results.append(result)
        
        status = "通过" if result['success'] else "失败"
        self.logger.info(f"测试 {test_name} {status} (耗时: {result['duration']:.2f}s)")
        
        return result
    
    def generate_report(self) -> str:
        """生成测试报告"""
        return TestUtils.create_test_report(self.results)
    
    def save_report(self, filename: str = "ocr_test_report.txt"):
        """保存测试报告"""
        report = self.generate_report()
        
        report_path = project_root / "tests" / filename
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        self.logger.info(f"测试报告已保存: {report_path}")
        return report_path
