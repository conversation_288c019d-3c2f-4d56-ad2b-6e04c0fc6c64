#!/usr/bin/env python3
"""
端到端OCR测试
模拟完整的用户工作流程
"""

import sys
import os
import time
import tempfile
import shutil
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def test_complete_workflow():
    """测试完整的OCR工作流程"""
    print("端到端OCR工作流程测试")
    print("=" * 50)
    
    # 检查测试文件
    test_files = []
    if (project_root / "ocr测试.png").exists():
        test_files.append(project_root / "ocr测试.png")
    if (project_root / "ocr测试.pdf").exists():
        test_files.append(project_root / "ocr测试.pdf")
    
    if not test_files:
        print("❌ 未找到测试文件 (ocr测试.png 或 ocr测试.pdf)")
        return False
    
    try:
        from webui.api_bridge import APIBridge
        from webui.web_controller import WebController
        
        # 创建Web控制器和API桥接
        print("🔧 初始化系统组件...")
        web_controller = WebController(debug=True)
        api_bridge = APIBridge()
        api_bridge.set_window_controller(web_controller)
        
        # 创建临时输出目录
        output_dir = Path(tempfile.mkdtemp(prefix="ocr_end_to_end_"))
        print(f"📁 输出目录: {output_dir}")
        
        results = []
        
        for test_file in test_files:
            print(f"\n📄 处理文件: {test_file.name}")
            
            # 步骤1: 选择文件
            print("  1. 选择文件...")
            select_result = api_bridge.select_file(str(test_file))
            if not select_result.get('success'):
                print(f"    ❌ 文件选择失败: {select_result.get('error')}")
                continue
            
            print(f"    ✅ 文件选择成功: {select_result['file_info']}")
            
            # 测试不同的输出格式
            formats_to_test = ['txt', 'xlsx']
            
            for output_format in formats_to_test:
                print(f"\n  2. 开始处理 ({output_format.upper()} 格式)...")
                
                # 配置处理参数
                config = {
                    'mode': 'general',
                    'translation': {
                        'enabled': False
                    },
                    'output': {
                        'format': output_format,
                        'directory': str(output_dir),
                        'merge_tables': False
                    }
                }
                
                # 开始处理
                start_result = api_bridge.start_processing(config)
                if not start_result.get('success'):
                    print(f"    ❌ 处理启动失败: {start_result.get('error')}")
                    continue
                
                print("    ✅ 处理已启动，监控进度...")
                
                # 监控处理进度
                max_wait_time = 120  # 最大等待2分钟
                start_time = time.time()
                
                while time.time() - start_time < max_wait_time:
                    status = api_bridge.get_processing_status()
                    
                    print(f"    📊 进度: {status['progress']}% - {status['status_text']}")
                    
                    if status.get('error'):
                        print(f"    ❌ 处理失败: {status['error']}")
                        break
                    
                    if not status['is_processing']:
                        if status['progress'] == 100:
                            print("    ✅ 处理完成！")
                            
                            # 检查输出信息
                            if 'output_info' in status:
                                output_info = status['output_info']
                                output_path = Path(output_info['output_path'])
                                
                                if output_path.exists():
                                    file_size = output_path.stat().st_size
                                    print(f"    📄 输出文件: {output_path}")
                                    print(f"    📏 文件大小: {file_size} bytes")
                                    
                                    # 记录结果
                                    results.append({
                                        'input_file': test_file.name,
                                        'output_format': output_format,
                                        'output_path': output_path,
                                        'file_size': file_size,
                                        'statistics': output_info.get('statistics', {}),
                                        'success': True
                                    })
                                    
                                    # 测试完成对话框信息
                                    completion_result = api_bridge.show_completion_dialog(output_info)
                                    if completion_result.get('success'):
                                        print("    ✅ 完成对话框信息生成成功")
                                    
                                else:
                                    print(f"    ❌ 输出文件不存在: {output_path}")
                                    results.append({
                                        'input_file': test_file.name,
                                        'output_format': output_format,
                                        'success': False,
                                        'error': '输出文件不存在'
                                    })
                            else:
                                print("    ⚠️  未找到输出信息")
                                results.append({
                                    'input_file': test_file.name,
                                    'output_format': output_format,
                                    'success': False,
                                    'error': '未找到输出信息'
                                })
                        break
                    
                    time.sleep(1)
                else:
                    print("    ⏰ 处理超时")
                    results.append({
                        'input_file': test_file.name,
                        'output_format': output_format,
                        'success': False,
                        'error': '处理超时'
                    })
        
        # 生成测试报告
        print(f"\n{'='*50}")
        print("测试结果汇总")
        print(f"{'='*50}")
        
        successful_results = [r for r in results if r.get('success')]
        failed_results = [r for r in results if not r.get('success')]
        
        print(f"总测试数: {len(results)}")
        print(f"成功: {len(successful_results)}")
        print(f"失败: {len(failed_results)}")
        print(f"成功率: {(len(successful_results)/len(results)*100):.1f}%")
        
        print(f"\n✅ 成功的测试:")
        for result in successful_results:
            print(f"  📄 {result['input_file']} → {result['output_format'].upper()}")
            print(f"     文件: {result['output_path']}")
            print(f"     大小: {result['file_size']} bytes")
            if 'statistics' in result:
                stats = result['statistics']
                if 'total_pages' in stats:
                    print(f"     页数: {stats['total_pages']}")
                if 'text_blocks' in stats:
                    print(f"     文本块: {stats['text_blocks']}")
        
        if failed_results:
            print(f"\n❌ 失败的测试:")
            for result in failed_results:
                print(f"  📄 {result['input_file']} → {result['output_format'].upper()}")
                print(f"     错误: {result.get('error', '未知错误')}")
        
        # 显示输出文件列表
        print(f"\n📁 生成的文件:")
        output_files = list(output_dir.glob("*"))
        if output_files:
            for file_path in output_files:
                size = file_path.stat().st_size
                print(f"  📄 {file_path.name} ({size} bytes)")
            
            # 保留输出文件供用户查看
            final_output_dir = project_root / f"ocr_test_output_{int(time.time())}"
            shutil.copytree(output_dir, final_output_dir)
            print(f"\n📂 测试结果已保存到: {final_output_dir}")
        else:
            print("  (无文件生成)")
        
        # 清理临时目录
        try:
            shutil.rmtree(output_dir)
        except Exception as e:
            print(f"⚠️  清理临时目录失败: {e}")
        
        # 判断测试是否成功
        if len(successful_results) > 0:
            print(f"\n🎉 端到端测试成功！")
            print("✅ OCR处理流程正常工作")
            print("✅ 文件输出功能正常")
            print("✅ 完成提示功能正常")
            return True
        else:
            print(f"\n❌ 端到端测试失败！")
            print("所有处理都失败了")
            return False
        
    except Exception as e:
        print(f"❌ 端到端测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    try:
        success = test_complete_workflow()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
        return 1
    except Exception as e:
        print(f"\n测试过程发生未知错误: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
