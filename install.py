#!/usr/bin/env python3
"""
Prisma OCR & Translator 安装脚本
"""

import sys
import os
import subprocess
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    if sys.version_info < (3, 8):
        print(f"❌ Python版本过低: {sys.version}")
        print("需要Python 3.8或更高版本")
        return False
    else:
        print(f"✅ Python版本: {sys.version}")
        return True

def install_dependencies():
    """安装依赖包"""
    print("\n安装依赖包...")

    requirements_file = Path(__file__).parent / "requirements.txt"
    if not requirements_file.exists():
        print("❌ requirements.txt 文件不存在")
        return False

    try:
        # 升级pip
        print("升级pip...")
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"],
                      check=True, capture_output=True)

        # 安装依赖
        print("安装项目依赖...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ], capture_output=True, text=True)

        if result.returncode == 0:
            print("✅ 依赖包安装成功")
            return True
        else:
            print(f"❌ 依赖包安装失败:")
            print(result.stderr)
            return False

    except subprocess.CalledProcessError as e:
        print(f"❌ 安装过程出错: {e}")
        return False

def install_paddleocr_full():
    """安装PaddleOCR全功能版本（CPU版本）"""
    print("\n安装PaddleOCR全功能版本...")

    try:
        # 首先安装PaddlePaddle CPU版本
        print("安装PaddlePaddle CPU版本...")
        paddle_result = subprocess.run([
            sys.executable, "-m", "pip", "install",
            "paddlepaddle==3.0.0",
            "-i", "https://www.paddlepaddle.org.cn/packages/stable/cpu/"
        ], capture_output=True, text=True)

        if paddle_result.returncode != 0:
            print(f"❌ PaddlePaddle安装失败:")
            print(paddle_result.stderr)
            return False

        print("✅ PaddlePaddle CPU版本安装成功")

        # 安装PaddleOCR全功能版本
        print("安装PaddleOCR全功能版本（包含文档解析、文档理解、文档翻译、关键信息抽取等功能）...")
        ocr_result = subprocess.run([
            sys.executable, "-m", "pip", "install", "paddleocr[all]"
        ], capture_output=True, text=True)

        if ocr_result.returncode != 0:
            print(f"❌ PaddleOCR全功能版本安装失败:")
            print(ocr_result.stderr)
            return False

        print("✅ PaddleOCR全功能版本安装成功")

        # 验证安装
        print("验证PaddleOCR安装...")
        verify_result = subprocess.run([
            sys.executable, "-c",
            "import paddle; import paddleocr; print('PaddleOCR安装验证成功')"
        ], capture_output=True, text=True)

        if verify_result.returncode == 0:
            print("✅ PaddleOCR安装验证成功")
            return True
        else:
            print(f"❌ PaddleOCR安装验证失败:")
            print(verify_result.stderr)
            return False

    except subprocess.CalledProcessError as e:
        print(f"❌ PaddleOCR安装过程出错: {e}")
        return False

def create_directories():
    """创建必要的目录"""
    print("\n创建目录结构...")
    
    directories = [
        "logs",
        "resources/icons",
        "resources/styles", 
        "resources/models",
    ]
    
    project_root = Path(__file__).parent
    
    for dir_path in directories:
        full_path = project_root / dir_path
        full_path.mkdir(parents=True, exist_ok=True)
        print(f"✅ 创建目录: {dir_path}")
    
    return True

def test_installation():
    """测试安装"""
    print("\n测试安装...")

    try:
        # 运行PaddleOCR安装验证测试
        paddleocr_test = Path(__file__).parent / "tests" / "test_paddleocr_installation.py"
        if paddleocr_test.exists():
            print("运行PaddleOCR安装验证测试...")
            result = subprocess.run([sys.executable, str(paddleocr_test)],
                                  capture_output=True, text=True)

            if result.returncode == 0:
                print("✅ PaddleOCR安装验证测试通过")
            else:
                print("❌ PaddleOCR安装验证测试失败")
                print(result.stdout)
                print(result.stderr)

        # 运行基础测试
        test_script = Path(__file__).parent / "tests" / "test_basic.py"
        if test_script.exists():
            print("运行基础功能测试...")
            result = subprocess.run([sys.executable, str(test_script)],
                                  capture_output=True, text=True)

            if result.returncode == 0:
                print("✅ 基础功能测试通过")
                return True
            else:
                print("❌ 基础功能测试失败")
                print(result.stdout)
                print(result.stderr)
                return False
        else:
            print("⚠️  基础测试脚本不存在，跳过测试")
            return True

    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        return False

def show_next_steps():
    """显示后续步骤"""
    print("\n" + "="*50)
    print("🎉 安装完成！")
    print("\n后续步骤:")
    print("1. 运行应用程序:")
    print("   python main.py")
    print("   或者")
    print("   python run.py")
    print("\n2. 首次运行时会自动下载模型文件，请耐心等待")
    print("\n3. 如遇问题，请查看以下文档:")
    print("   - README.md")
    print("   - docs/OCR_INSTALLATION_GUIDE.md")
    print("\n4. 运行测试:")
    print("   python test_basic.py")
    print("\n5. PaddleOCR功能说明:")
    print("   - 基础OCR：文字识别和位置检测")
    print("   - 文档解析：表格、公式、印章、图片等版面元素提取")
    print("   - 文档理解：关键信息抽取")
    print("   - 文档翻译：多语言文档翻译")

def main():
    """主安装函数"""
    print("Prisma OCR & Translator 安装程序")
    print("="*50)

    # 检查Python版本
    if not check_python_version():
        return 1

    # 安装PaddleOCR全功能版本
    if not install_paddleocr_full():
        print("\n❌ 安装失败：PaddleOCR安装出错")
        return 1

    # 安装其他依赖
    if not install_dependencies():
        print("\n❌ 安装失败：依赖包安装出错")
        return 1

    # 创建目录
    if not create_directories():
        print("\n❌ 安装失败：目录创建出错")
        return 1

    # 测试安装
    if not test_installation():
        print("\n⚠️  安装完成但测试失败，可能存在问题")
        print("请检查依赖包是否正确安装")

    # 显示后续步骤
    show_next_steps()

    return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n安装被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n安装过程发生未知错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
