# PaddleOCR 环境安装指南

## 概述

本文档详细说明了如何在 Prisma OCR & Translator 项目中安装和配置 PaddleOCR 环境，特别是 CPU 版本的全功能安装。此文档为后续打包为 exe 文件提供完整的参考。

## 系统要求

### 基础要求
- **操作系统**: Windows 10/11, Linux, macOS
- **Python 版本**: 3.8 或更高版本（推荐 3.9-3.11）
- **内存**: 至少 4GB RAM（推荐 8GB 或更多）
- **存储空间**: 至少 2GB 可用空间（用于模型文件）

### Python 环境检查
```bash
python --version
# 应输出: Python 3.8.x 或更高版本
```

## 安装方式

### 方式一：使用项目安装脚本（推荐）

项目提供了自动化安装脚本 `install.py`，它会自动安装 PaddleOCR 全功能版本：

```bash
python install.py
```

安装脚本会执行以下步骤：
1. 检查 Python 版本
2. 安装 PaddlePaddle CPU 版本
3. 安装 PaddleOCR 全功能版本
4. 安装项目其他依赖
5. 创建必要的目录结构
6. 运行基础测试

### 方式二：手动安装

如果需要手动安装，请按以下步骤操作：

#### 1. 升级 pip
```bash
python -m pip install --upgrade pip
```

#### 2. 安装 PaddlePaddle CPU 版本
```bash
python -m pip install paddlepaddle==3.0.0 -i https://www.paddlepaddle.org.cn/packages/stable/cpu/
```

#### 3. 安装 PaddleOCR 全功能版本
```bash
python -m pip install "paddleocr[all]"
```

#### 4. 验证安装
```bash
python -c "import paddle; import paddleocr; print('PaddleOCR安装验证成功')"
```

## PaddleOCR 功能说明

### 基础功能
- **文字识别**: 返回文字位置坐标和文本内容
- **多语言支持**: 支持中文、英文等多种语言

### 全功能版本额外功能
安装 `paddleocr[all]` 后，可使用以下高级功能：

#### 1. 文档解析 (doc-parser)
- 提取文档中的表格、公式、印章、图片等版面元素
- 包含 PP-StructureV3 等模型方案
- 支持复杂文档结构分析

#### 2. 信息抽取 (ie)
- 从文档中提取关键信息，如姓名、日期、地址、金额等
- 包含 PP-ChatOCRv4 等模型方案
- 支持智能信息理解

#### 3. 文档翻译 (trans)
- 将文档从一种语言翻译为另一种语言
- 包含 PP-DocTranslation 等模型方案
- 保持文档格式和布局

## 依赖组说明

PaddleOCR 提供了多个依赖组，可根据需要选择安装：

| 依赖组名称 | 对应功能 | 安装命令 |
|-----------|----------|----------|
| 基础版本 | 基础文字识别功能 | `pip install paddleocr` |
| doc-parser | 文档解析功能 | `pip install "paddleocr[doc-parser]"` |
| ie | 信息抽取功能 | `pip install "paddleocr[ie]"` |
| trans | 文档翻译功能 | `pip install "paddleocr[trans]"` |
| all | 完整功能 | `pip install "paddleocr[all]"` |

## 模型文件说明

### 自动下载
PaddleOCR 在首次使用时会自动下载所需的模型文件：
- **存储位置**: `~/.paddleocr/` 目录
- **文件大小**: 约 100-500MB（取决于使用的模型）
- **下载时间**: 根据网络速度，通常需要 5-15 分钟

### 模型类型
- **检测模型**: 用于文字区域检测
- **识别模型**: 用于文字内容识别
- **分类模型**: 用于文字方向分类
- **结构化模型**: 用于表格、公式等结构化内容识别

## 常见问题与解决方案

### 1. 安装失败
**问题**: pip 安装失败或网络超时
**解决方案**:
```bash
# 使用国内镜像源
python -m pip install paddlepaddle==3.0.0 -i https://pypi.tuna.tsinghua.edu.cn/simple/
python -m pip install "paddleocr[all]" -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 2. 模型下载失败
**问题**: 首次运行时模型下载失败
**解决方案**:
- 检查网络连接
- 使用代理或 VPN
- 手动下载模型文件到指定目录

### 3. 内存不足
**问题**: 运行时出现内存不足错误
**解决方案**:
- 关闭其他占用内存的程序
- 使用较小的模型
- 增加系统虚拟内存

### 4. Python 版本不兼容
**问题**: Python 版本过低或过高
**解决方案**:
- 升级到 Python 3.8-3.11
- 使用虚拟环境隔离不同版本

## 性能优化建议

### 1. 硬件优化
- **CPU**: 使用多核 CPU 可提升处理速度
- **内存**: 8GB 或更多内存可避免频繁的磁盘交换
- **存储**: SSD 硬盘可加快模型加载速度

### 2. 软件优化
- 使用合适的模型大小
- 批量处理多个文件
- 缓存常用模型

## 打包为 exe 的注意事项

### 1. 依赖文件
打包时需要包含以下文件：
- PaddlePaddle 库文件
- PaddleOCR 库文件
- 模型文件（可选，也可运行时下载）
- 相关的 DLL 文件（Windows）

### 2. 推荐的打包工具
- **PyInstaller**: 最常用的 Python 打包工具
- **cx_Freeze**: 跨平台打包工具
- **Nuitka**: 编译型打包工具

### 3. 打包命令示例
```bash
# 使用 PyInstaller
pyinstaller --onefile --add-data "models;models" main.py

# 包含所有依赖
pyinstaller --onefile --hidden-import paddleocr --hidden-import paddle main.py
```

### 4. 注意事项
- 打包后的文件可能较大（500MB-2GB）
- 首次运行可能需要下载模型文件
- 确保目标系统有足够的权限和空间

## 测试验证

### 基础功能测试
```python
import paddleocr

# 初始化OCR
ocr = paddleocr.PaddleOCR(use_angle_cls=True, lang='ch')

# 测试图片识别
result = ocr.ocr('test_image.jpg', cls=True)
print(result)
```

### 全功能测试
运行项目提供的测试脚本：
```bash
python tests/test_basic.py
python tests/test_ocr_comprehensive.py
```

## 版本信息

- **PaddlePaddle**: 3.0.0
- **PaddleOCR**: 最新版本（支持 [all] 依赖组）
- **文档版本**: 1.0
- **更新日期**: 2024年

## 技术支持

如遇到问题，请参考：
1. [PaddleOCR 官方文档](https://paddleocr.readthedocs.io/)
2. [PaddleOCR GitHub 仓库](https://github.com/PaddlePaddle/PaddleOCR)
3. 项目的 `README.md` 文件
4. 项目的测试和故障排除文档
