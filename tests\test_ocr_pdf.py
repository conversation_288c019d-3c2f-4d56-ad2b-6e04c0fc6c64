#!/usr/bin/env python3
"""
PDF OCR测试脚本
使用ocr测试.pdf进行OCR功能测试
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_dependencies():
    """检查必要的依赖"""
    print("检查依赖包...")
    
    missing_deps = []
    
    # 检查基础依赖
    try:
        import numpy as np
        print("✓ NumPy 可用")
    except ImportError:
        missing_deps.append("numpy")
        print("✗ NumPy 不可用")
    
    try:
        from PIL import Image
        print("✓ Pillow 可用")
    except ImportError:
        missing_deps.append("Pillow")
        print("✗ Pillow 不可用")
    
    try:
        import cv2
        print("✓ OpenCV 可用")
    except ImportError:
        missing_deps.append("opencv-python")
        print("✗ OpenCV 不可用")
    
    try:
        import pdf2image
        print("✓ pdf2image 可用")
    except ImportError:
        missing_deps.append("pdf2image")
        print("✗ pdf2image 不可用")
    
    # 检查PaddleOCR相关
    try:
        import paddle
        print("✓ PaddlePaddle 可用")
    except ImportError:
        missing_deps.append("paddlepaddle")
        print("✗ PaddlePaddle 不可用")
    
    try:
        import paddleocr
        print("✓ PaddleOCR 可用")
    except ImportError:
        missing_deps.append("paddleocr")
        print("✗ PaddleOCR 不可用")
    
    if missing_deps:
        print(f"\n缺少依赖包: {', '.join(missing_deps)}")
        print("请运行: pip install " + " ".join(missing_deps))
        return False
    
    return True

def test_pdf_to_image():
    """测试PDF转图像功能"""
    print("\n测试PDF转图像...")
    
    pdf_path = project_root / "ocr测试.pdf"
    if not pdf_path.exists():
        print("✗ 测试PDF文件不存在")
        return None
    
    try:
        from utils.image_utils import ImageUtils
        
        # 转换PDF为图像
        print("正在转换PDF为图像...")
        images = ImageUtils.convert_pdf_to_images(pdf_path, dpi=150)
        
        print(f"✓ PDF转换成功，共 {len(images)} 页")
        
        # 显示第一页的信息
        if images:
            first_image = images[0]
            print(f"  - 第一页尺寸: {first_image.size}")
            print(f"  - 图像模式: {first_image.mode}")
        
        return images
        
    except Exception as e:
        print(f"✗ PDF转换失败: {e}")
        return None

def test_simple_ocr(images):
    """测试简单OCR功能"""
    print("\n测试OCR识别...")
    
    if not images:
        print("✗ 没有图像可供测试")
        return
    
    try:
        # 使用简化的OCR测试（不依赖完整的OCR引擎）
        from utils.image_utils import ImageUtils
        import numpy as np
        
        # 转换第一页为OpenCV格式
        first_image = images[0]
        cv_image = ImageUtils.pil_to_cv2(first_image)
        
        print(f"✓ 图像转换成功，尺寸: {cv_image.shape}")
        
        # 尝试使用PaddleOCR进行识别
        try:
            from paddleocr import PaddleOCR
            
            print("正在初始化PaddleOCR...")
            ocr = PaddleOCR(use_angle_cls=True, lang='ch', show_log=False)
            
            print("正在进行OCR识别...")
            start_time = time.time()
            result = ocr.ocr(cv_image, cls=True)
            end_time = time.time()
            
            print(f"✓ OCR识别完成，耗时: {end_time - start_time:.2f}秒")
            
            # 解析结果
            if result and result[0]:
                print(f"识别到 {len(result[0])} 个文本块:")
                for i, line in enumerate(result[0][:5]):  # 只显示前5个结果
                    if len(line) >= 2:
                        text = line[1][0] if isinstance(line[1], (list, tuple)) else str(line[1])
                        confidence = line[1][1] if isinstance(line[1], (list, tuple)) and len(line[1]) > 1 else 0.0
                        print(f"  {i+1}. {text} (置信度: {confidence:.2f})")
                
                if len(result[0]) > 5:
                    print(f"  ... 还有 {len(result[0]) - 5} 个文本块")
                
                return True
            else:
                print("✗ 未识别到任何文本")
                return False
                
        except Exception as e:
            print(f"✗ PaddleOCR识别失败: {e}")
            return False
            
    except Exception as e:
        print(f"✗ OCR测试失败: {e}")
        return False

def test_file_utils():
    """测试文件工具功能"""
    print("\n测试文件工具...")
    
    try:
        from utils.file_utils import FileUtils
        
        pdf_path = project_root / "ocr测试.pdf"
        
        # 测试文件格式检查
        is_supported = FileUtils.is_supported_input_file(pdf_path)
        print(f"✓ PDF格式支持检查: {is_supported}")
        
        is_pdf = FileUtils.is_pdf_file(pdf_path)
        print(f"✓ PDF文件检查: {is_pdf}")
        
        # 测试文件大小
        file_size_mb = FileUtils.get_file_size_mb(pdf_path)
        print(f"✓ 文件大小: {file_size_mb:.2f} MB")
        
        # 测试输出文件名生成
        output_path = FileUtils.get_output_filename(pdf_path, 'txt')
        print(f"✓ 输出文件名: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"✗ 文件工具测试失败: {e}")
        return False

def test_configuration():
    """测试配置系统"""
    print("\n测试配置系统...")
    
    try:
        from config.settings import AppSettings
        from config.models_config import ModelsConfig
        
        # 测试应用设置
        settings = AppSettings()
        print(f"✓ 应用设置加载成功")
        print(f"  - OCR语言: {settings.ocr.lang}")
        print(f"  - 使用GPU: {settings.ocr.use_gpu}")
        
        # 测试模型配置
        models_config = ModelsConfig()
        all_models = models_config.get_all_models()
        print(f"✓ 模型配置加载成功，共 {len(all_models)} 个模型")
        
        missing_models = models_config.get_missing_models()
        if missing_models:
            print(f"  - 缺少 {len(missing_models)} 个模型文件")
            print("  - 首次运行时会自动下载")
        else:
            print("  - 所有模型文件已就绪")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置系统测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("Prisma OCR PDF测试")
    print("=" * 40)
    print(f"测试文件: ocr测试.pdf")
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请先安装必要的依赖包")
        return 1
    
    # 测试配置系统
    if not test_configuration():
        print("\n❌ 配置系统测试失败")
        return 1
    
    # 测试文件工具
    if not test_file_utils():
        print("\n❌ 文件工具测试失败")
        return 1
    
    # 测试PDF转图像
    images = test_pdf_to_image()
    if not images:
        print("\n❌ PDF转图像测试失败")
        return 1
    
    # 测试OCR识别
    if not test_simple_ocr(images):
        print("\n❌ OCR识别测试失败")
        return 1
    
    print("\n" + "=" * 40)
    print("🎉 所有测试通过！")
    print("PDF OCR功能基本正常")
    
    return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程发生未知错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
