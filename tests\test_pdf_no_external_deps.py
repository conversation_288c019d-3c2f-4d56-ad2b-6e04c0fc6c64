#!/usr/bin/env python3
"""
测试无外部依赖的PDF转图像功能
验证PyMuPDF方案是否正常工作
"""

import sys
import os
import time
import shutil
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def test_pymupdf_installation():
    """测试PyMuPDF是否已安装"""
    print("检查PyMuPDF安装状态...")
    
    try:
        import fitz
        print(f"[PASS] PyMuPDF已安装，版本: {fitz.version}")
        return True
    except ImportError:
        print("[FAIL] PyMuPDF未安装")
        print("请运行: pip install PyMuPDF")
        return False


def test_pdf_conversion_methods():
    """测试不同的PDF转换方法"""
    print("\n测试PDF转换方法...")
    
    # 准备测试文件
    pdf_source = project_root / "ocr测试.pdf"
    if not pdf_source.exists():
        print("[SKIP] 测试PDF文件不存在")
        return True
    
    # 复制到临时目录避免中文路径问题
    temp_dir = Path(tempfile.mkdtemp(prefix="pdf_test_"))
    pdf_path = temp_dir / "test.pdf"
    shutil.copy2(pdf_source, pdf_path)
    
    try:
        from utils.image_utils import ImageUtils
        
        # 测试1: 使用默认方法（应该优先使用PyMuPDF）
        print("测试1: 默认转换方法")
        start_time = time.time()
        
        try:
            images = ImageUtils.convert_pdf_to_images(pdf_path, dpi=150, first_page=1, last_page=1)
            end_time = time.time()
            
            if images:
                print(f"[PASS] 转换成功，{len(images)} 页，耗时: {end_time - start_time:.2f}s")
                print(f"  第一页尺寸: {images[0].size}")
                print(f"  图像模式: {images[0].mode}")
            else:
                print("[FAIL] 转换结果为空")
                return False
                
        except Exception as e:
            print(f"[FAIL] 转换失败: {e}")
            return False
        
        # 测试2: 直接测试PyMuPDF方法
        print("\n测试2: 直接使用PyMuPDF方法")
        start_time = time.time()
        
        try:
            images = ImageUtils._convert_pdf_with_pymupdf(pdf_path, dpi=150, first_page=1, last_page=1)
            end_time = time.time()
            
            if images:
                print(f"[PASS] PyMuPDF转换成功，{len(images)} 页，耗时: {end_time - start_time:.2f}s")
                print(f"  第一页尺寸: {images[0].size}")
            else:
                print("[FAIL] PyMuPDF转换结果为空")
                return False
                
        except Exception as e:
            print(f"[FAIL] PyMuPDF转换失败: {e}")
            return False
        
        # 测试3: 测试多页转换
        print("\n测试3: 多页转换")
        start_time = time.time()
        
        try:
            all_images = ImageUtils.convert_pdf_to_images(pdf_path, dpi=100)
            end_time = time.time()
            
            if all_images:
                print(f"[PASS] 多页转换成功，{len(all_images)} 页，耗时: {end_time - start_time:.2f}s")
                for i, img in enumerate(all_images[:3]):  # 只显示前3页信息
                    print(f"  第{i+1}页尺寸: {img.size}")
            else:
                print("[FAIL] 多页转换结果为空")
                return False
                
        except Exception as e:
            print(f"[FAIL] 多页转换失败: {e}")
            return False
        
        return True
        
    finally:
        # 清理临时文件
        try:
            shutil.rmtree(temp_dir)
        except Exception as e:
            print(f"[WARN] 清理临时文件失败: {e}")


def test_pdf_conversion_performance():
    """测试PDF转换性能"""
    print("\n测试PDF转换性能...")
    
    pdf_source = project_root / "ocr测试.pdf"
    if not pdf_source.exists():
        print("[SKIP] 测试PDF文件不存在")
        return True
    
    temp_dir = Path(tempfile.mkdtemp(prefix="pdf_perf_test_"))
    pdf_path = temp_dir / "test.pdf"
    shutil.copy2(pdf_source, pdf_path)
    
    try:
        from utils.image_utils import ImageUtils
        import psutil
        
        # 记录初始内存
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 测试不同DPI的性能
        dpi_settings = [100, 150, 200]
        
        for dpi in dpi_settings:
            print(f"测试DPI {dpi}...")
            
            start_time = time.time()
            before_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            try:
                images = ImageUtils.convert_pdf_to_images(pdf_path, dpi=dpi, first_page=1, last_page=1)
                
                end_time = time.time()
                after_memory = process.memory_info().rss / 1024 / 1024  # MB
                
                duration = end_time - start_time
                memory_used = after_memory - before_memory
                
                if images:
                    print(f"  [PASS] DPI {dpi}: {duration:.2f}s, 内存: +{memory_used:.1f}MB, 尺寸: {images[0].size}")
                else:
                    print(f"  [FAIL] DPI {dpi}: 转换失败")
                
                # 清理图像以释放内存
                del images
                
            except Exception as e:
                print(f"  [FAIL] DPI {dpi}: {e}")
        
        # 检查内存泄漏
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        if memory_increase < 50:  # 50MB以下认为正常
            print(f"[PASS] 内存使用正常，总增加: {memory_increase:.1f}MB")
        else:
            print(f"[WARN] 内存使用较高，总增加: {memory_increase:.1f}MB")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] 性能测试异常: {e}")
        return False
        
    finally:
        try:
            shutil.rmtree(temp_dir)
        except Exception as e:
            print(f"[WARN] 清理临时文件失败: {e}")


def compare_pdf_methods():
    """比较不同PDF转换方法"""
    print("\n比较PDF转换方法...")
    
    pdf_source = project_root / "ocr测试.pdf"
    if not pdf_source.exists():
        print("[SKIP] 测试PDF文件不存在")
        return True
    
    temp_dir = Path(tempfile.mkdtemp(prefix="pdf_compare_"))
    pdf_path = temp_dir / "test.pdf"
    shutil.copy2(pdf_source, pdf_path)
    
    try:
        from utils.image_utils import ImageUtils
        
        methods = []
        
        # 测试PyMuPDF
        try:
            start_time = time.time()
            images_pymupdf = ImageUtils._convert_pdf_with_pymupdf(pdf_path, dpi=150, first_page=1, last_page=1)
            pymupdf_time = time.time() - start_time
            
            methods.append({
                'name': 'PyMuPDF',
                'time': pymupdf_time,
                'success': len(images_pymupdf) > 0,
                'size': images_pymupdf[0].size if images_pymupdf else None,
                'dependency': '无外部依赖'
            })
        except Exception as e:
            methods.append({
                'name': 'PyMuPDF',
                'time': 0,
                'success': False,
                'error': str(e),
                'dependency': '无外部依赖'
            })
        
        # 测试pdf2image（如果Poppler可用）
        try:
            import pdf2image
            start_time = time.time()
            images_pdf2image = pdf2image.convert_from_path(pdf_path, dpi=150, first_page=1, last_page=1)
            pdf2image_time = time.time() - start_time
            
            methods.append({
                'name': 'pdf2image',
                'time': pdf2image_time,
                'success': len(images_pdf2image) > 0,
                'size': images_pdf2image[0].size if images_pdf2image else None,
                'dependency': '需要Poppler'
            })
        except Exception as e:
            methods.append({
                'name': 'pdf2image',
                'time': 0,
                'success': False,
                'error': str(e),
                'dependency': '需要Poppler'
            })
        
        # 显示比较结果
        print("方法比较结果:")
        print("-" * 60)
        for method in methods:
            status = "[PASS]" if method['success'] else "[FAIL]"
            name = method['name']
            dependency = method['dependency']
            
            print(f"{status} {name} ({dependency})")
            
            if method['success']:
                print(f"  耗时: {method['time']:.2f}s")
                print(f"  图像尺寸: {method['size']}")
            else:
                print(f"  错误: {method.get('error', '未知错误')}")
        
        # 推荐
        successful_methods = [m for m in methods if m['success']]
        if successful_methods:
            fastest = min(successful_methods, key=lambda x: x['time'])
            no_deps = [m for m in successful_methods if '无外部依赖' in m['dependency']]
            
            print(f"\n推荐:")
            if no_deps:
                print(f"- 推荐使用: {no_deps[0]['name']} (无外部依赖)")
            else:
                print(f"- 最快方法: {fastest['name']} ({fastest['time']:.2f}s)")
        
        return True
        
    finally:
        try:
            shutil.rmtree(temp_dir)
        except Exception as e:
            print(f"[WARN] 清理临时文件失败: {e}")


def main():
    """主测试函数"""
    print("PDF转图像无外部依赖方案测试")
    print("=" * 50)
    
    test_results = []
    
    # 1. 检查PyMuPDF安装
    result = test_pymupdf_installation()
    test_results.append(("PyMuPDF安装检查", result))
    
    if not result:
        print("\n[ERROR] PyMuPDF未安装，无法继续测试")
        print("请运行: pip install PyMuPDF")
        return 1
    
    # 2. 测试PDF转换方法
    result = test_pdf_conversion_methods()
    test_results.append(("PDF转换方法", result))
    
    # 3. 测试性能
    result = test_pdf_conversion_performance()
    test_results.append(("性能测试", result))
    
    # 4. 比较不同方法
    result = compare_pdf_methods()
    test_results.append(("方法比较", result))
    
    # 生成报告
    print(f"\n{'='*50}")
    print("测试结果汇总")
    print(f"{'='*50}")
    
    passed = sum(1 for _, success in test_results if success)
    total = len(test_results)
    
    print(f"总测试数: {total}")
    print(f"通过: {passed}")
    print(f"失败: {total - passed}")
    print(f"成功率: {(passed/total*100):.1f}%")
    
    print(f"\n详细结果:")
    for test_name, success in test_results:
        status = "[PASS]" if success else "[FAIL]"
        print(f"  {status} {test_name}")
    
    if passed == total:
        print(f"\n[SUCCESS] 所有测试通过！")
        print("- PyMuPDF方案完全可用")
        print("- 无需外部依赖即可处理PDF")
        print("- 系统已支持完整的PDF转图像功能")
        return 0
    else:
        print(f"\n[WARNING] 部分测试失败")
        print("- 请检查PyMuPDF安装")
        print("- 确认测试文件存在")
        return 1


if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程发生未知错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
