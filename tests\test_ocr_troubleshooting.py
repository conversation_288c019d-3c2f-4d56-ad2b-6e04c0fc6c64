"""
OCR故障排除和问题诊断测试
"""

import os
import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.ocr_engine import OCREngine
from config.settings import AppSettings
from config.models_config import ModelsConfig
from utils.ocr_diagnostics import OCRDiagnostics
from utils.image_utils import ImageUtils


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('ocr_troubleshooting.log', encoding='utf-8')
        ]
    )


def find_test_files():
    """查找测试文件"""
    test_files = []
    
    # 在项目根目录查找测试文件
    for pattern in ['*.png', '*.jpg', '*.jpeg', '*.pdf']:
        test_files.extend(project_root.glob(pattern))
    
    # 在tests目录查找
    tests_dir = project_root / 'tests'
    if tests_dir.exists():
        for pattern in ['*.png', '*.jpg', '*.jpeg', '*.pdf']:
            test_files.extend(tests_dir.glob(pattern))
    
    return test_files


def test_ocr_engine_initialization():
    """测试OCR引擎初始化"""
    print("\n=== OCR引擎初始化测试 ===")
    
    try:
        settings = AppSettings()
        models_config = ModelsConfig()
        ocr_engine = OCREngine(settings.ocr, models_config)
        
        print("✓ OCR引擎创建成功")
        
        # 设置回调函数
        def progress_callback(progress, status):
            print(f"  进度: {progress}% - {status}")
        
        def error_callback(error_msg):
            print(f"  错误: {error_msg}")
        
        ocr_engine.progress_callback = progress_callback
        ocr_engine.error_callback = error_callback
        
        # 初始化引擎
        print("正在初始化OCR引擎...")
        start_time = time.time()
        success = ocr_engine.initialize()
        init_time = time.time() - start_time
        
        if success:
            print(f"✓ OCR引擎初始化成功，耗时: {init_time:.2f}s")
            return ocr_engine
        else:
            print("✗ OCR引擎初始化失败")
            return None
            
    except Exception as e:
        print(f"✗ OCR引擎初始化异常: {e}")
        return None


def test_image_diagnostics(image_path):
    """测试图像诊断"""
    print(f"\n=== 图像诊断测试: {image_path.name} ===")
    
    try:
        diagnostics = OCRDiagnostics()
        
        # 诊断图像质量
        print("正在诊断图像质量...")
        diagnosis = diagnostics.diagnose_image_quality(image_path)
        
        if 'error' not in diagnosis:
            print(f"✓ 图像质量诊断完成")
            print(f"  整体质量: {diagnosis['overall_quality']}")
            print(f"  图像尺寸: {diagnosis['basic_info']['width']}x{diagnosis['basic_info']['height']}")
            print(f"  亮度状态: {diagnosis['brightness']['status']} (均值: {diagnosis['brightness']['mean']:.1f})")
            print(f"  对比度状态: {diagnosis['contrast']['status']} (比值: {diagnosis['contrast']['ratio']:.3f})")
            print(f"  清晰度状态: {diagnosis['sharpness']['status']}")
            print(f"  文本密度状态: {diagnosis['text_density']['status']}")
        else:
            print(f"✗ 图像质量诊断失败: {diagnosis['error']}")
        
        # 测试OCR参数
        print("正在测试OCR参数...")
        param_test = diagnostics.test_ocr_parameters(image_path)
        
        if 'error' not in param_test:
            print("✓ OCR参数测试完成")
            for name, result in param_test.items():
                if result['success']:
                    print(f"  {name}: {result['text_blocks']} 个文本块, 平均置信度: {result['avg_confidence']:.3f}")
                else:
                    print(f"  {name}: 失败 - {result.get('error', '未知错误')}")
        else:
            print(f"✗ OCR参数测试失败: {param_test['error']}")
        
        # 生成诊断报告
        print("正在生成诊断报告...")
        report_path = diagnostics.generate_diagnostic_report(image_path)
        if report_path:
            print(f"✓ 诊断报告已生成: {report_path}")
        else:
            print("✗ 诊断报告生成失败")
        
        return diagnosis, param_test
        
    except Exception as e:
        print(f"✗ 图像诊断测试异常: {e}")
        return None, None


def test_ocr_processing(ocr_engine, image_path):
    """测试OCR处理"""
    print(f"\n=== OCR处理测试: {image_path.name} ===")
    
    try:
        # 加载图像
        if image_path.suffix.lower() == '.pdf':
            print("正在转换PDF为图像...")
            images = ImageUtils.convert_pdf_to_images(image_path)
            if not images:
                print("✗ PDF转换失败")
                return None
            image = images[0]  # 使用第一页
            print(f"✓ PDF转换成功，使用第一页")
        else:
            image = ImageUtils.load_image_pil(image_path)
            print(f"✓ 图像加载成功")
        
        # 测试通用模式
        print("测试通用OCR模式...")
        ocr_engine.set_mode('general')
        
        start_time = time.time()
        result = ocr_engine.process_image(image, page_number=1)
        processing_time = time.time() - start_time
        
        print(f"✓ 通用模式处理完成，耗时: {processing_time:.2f}s")
        
        # 分析结果
        if hasattr(result, 'text_blocks') and result.text_blocks:
            print(f"✓ 识别到 {len(result.text_blocks)} 个文本块")
            
            # 显示前几个识别结果
            for i, block in enumerate(result.text_blocks[:3]):
                text = block.get('text', '').strip()
                confidence = block.get('confidence', 0.0)
                print(f"  文本块{i+1}: '{text[:50]}...' (置信度: {confidence:.3f})")
            
            if len(result.text_blocks) > 3:
                print(f"  ... 还有 {len(result.text_blocks) - 3} 个文本块")
            
            # 计算统计信息
            total_text = result.get_all_text()
            avg_confidence = result.get_average_confidence()
            
            print(f"  总文本长度: {len(total_text)} 字符")
            print(f"  平均置信度: {avg_confidence:.3f}")
            
            return result
        else:
            print("✗ 未识别到任何文本内容")
            
            # 尝试表格模式
            print("尝试表格模式...")
            ocr_engine.set_mode('table')
            
            start_time = time.time()
            table_result = ocr_engine.process_image(image, page_number=1)
            processing_time = time.time() - start_time
            
            print(f"✓ 表格模式处理完成，耗时: {processing_time:.2f}s")
            
            if hasattr(table_result, 'text_blocks') and table_result.text_blocks:
                print(f"✓ 表格模式识别到 {len(table_result.text_blocks)} 个文本块")
                return table_result
            else:
                print("✗ 表格模式也未识别到文本内容")
                return None
        
    except Exception as e:
        print(f"✗ OCR处理测试异常: {e}")
        import traceback
        traceback.print_exc()
        return None


def run_comprehensive_troubleshooting():
    """运行综合故障排除测试"""
    print("Prisma OCR 故障排除测试")
    print("=" * 50)
    
    # 设置日志
    setup_logging()
    
    # 查找测试文件
    test_files = find_test_files()
    if not test_files:
        print("✗ 未找到测试文件")
        return
    
    print(f"找到 {len(test_files)} 个测试文件:")
    for file in test_files:
        print(f"  - {file.name}")
    
    # 初始化OCR引擎
    ocr_engine = test_ocr_engine_initialization()
    if not ocr_engine:
        print("✗ OCR引擎初始化失败，无法继续测试")
        return
    
    # 测试每个文件
    successful_files = 0
    failed_files = 0
    
    for image_path in test_files[:3]:  # 限制测试前3个文件
        try:
            print(f"\n{'='*60}")
            print(f"测试文件: {image_path.name}")
            print(f"{'='*60}")
            
            # 图像诊断
            diagnosis, param_test = test_image_diagnostics(image_path)
            
            # OCR处理测试
            result = test_ocr_processing(ocr_engine, image_path)
            
            if result and hasattr(result, 'text_blocks') and result.text_blocks:
                successful_files += 1
                print(f"✓ 文件 {image_path.name} 测试成功")
            else:
                failed_files += 1
                print(f"✗ 文件 {image_path.name} 测试失败")
                
        except Exception as e:
            failed_files += 1
            print(f"✗ 文件 {image_path.name} 测试异常: {e}")
    
    # 总结
    print(f"\n{'='*60}")
    print("测试总结")
    print(f"{'='*60}")
    print(f"成功文件: {successful_files}")
    print(f"失败文件: {failed_files}")
    print(f"总成功率: {successful_files/(successful_files+failed_files)*100:.1f}%" if (successful_files+failed_files) > 0 else "0%")
    
    if failed_files > 0:
        print("\n故障排除建议:")
        print("1. 检查图像质量（亮度、对比度、清晰度）")
        print("2. 确认图像包含可识别的文本内容")
        print("3. 尝试不同的OCR参数配置")
        print("4. 考虑图像预处理（增强、去噪等）")
        print("5. 查看详细的诊断报告")


if __name__ == "__main__":
    run_comprehensive_troubleshooting()
