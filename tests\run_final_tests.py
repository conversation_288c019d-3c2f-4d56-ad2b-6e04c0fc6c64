#!/usr/bin/env python3
"""
最终测试执行脚本
运行所有关键测试并生成报告
"""

import sys
import os
import subprocess
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def run_test_script(script_name, description, timeout=300):
    """运行单个测试脚本"""
    print(f"\n{'='*60}")
    print(f"运行测试: {description}")
    print(f"脚本: {script_name}")
    print('='*60)
    
    script_path = project_root / 'tests' / script_name
    
    if not script_path.exists():
        print(f"[ERROR] 测试脚本不存在: {script_path}")
        return False, f"脚本不存在: {script_name}"
    
    start_time = time.time()
    
    try:
        result = subprocess.run([
            sys.executable, str(script_path)
        ], 
        capture_output=True, 
        text=True, 
        timeout=timeout,
        cwd=project_root
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        success = result.returncode == 0
        
        # 输出结果
        if result.stdout:
            print(result.stdout)
        
        if result.stderr and not success:
            print("错误输出:")
            print(result.stderr)
        
        status = "[PASS]" if success else "[FAIL]"
        print(f"\n{status} {description} (耗时: {duration:.1f}s)")
        
        return success, None if success else result.stderr
        
    except subprocess.TimeoutExpired:
        end_time = time.time()
        duration = end_time - start_time
        print(f"\n[TIMEOUT] {description} (超时: {timeout}s)")
        return False, f"测试超时 ({timeout}s)"
        
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        print(f"\n[ERROR] {description} - {e}")
        return False, str(e)


def main():
    """主函数"""
    print("Prisma OCR & Translator 最终测试套件")
    print("=" * 60)
    print("执行所有关键测试并生成综合报告")
    print(f"项目根目录: {project_root}")
    print(f"Python版本: {sys.version}")
    
    # 定义测试脚本
    test_scripts = [
        {
            'script': 'test_ocr_simple.py',
            'name': '基础功能测试',
            'description': '测试基本依赖、模块导入和配置系统',
            'timeout': 180,
            'critical': True
        },
        {
            'script': 'test_ocr_comprehensive.py',
            'name': '综合功能测试',
            'description': '测试图像处理、PDF处理和OCR引擎基本功能',
            'timeout': 300,
            'critical': True
        },
        {
            'script': 'test_ocr_final.py',
            'name': '最终验证测试',
            'description': '验证修复后的完整OCR功能',
            'timeout': 600,
            'critical': True
        }
    ]
    
    print(f"\n计划运行 {len(test_scripts)} 个测试脚本")
    
    # 显示测试计划
    print("\n测试计划:")
    for i, test_info in enumerate(test_scripts, 1):
        critical = " (关键)" if test_info['critical'] else ""
        print(f"{i:2d}. {test_info['name']}{critical}")
        print(f"     {test_info['description']}")
    
    # 确认运行
    try:
        response = input("\n开始运行测试？ [Y/n]: ").strip().lower()
        if response in ['n', 'no', '否']:
            print("测试已取消")
            return 0
    except KeyboardInterrupt:
        print("\n测试已取消")
        return 0
    
    # 运行测试
    test_results = []
    start_time = time.time()
    
    for i, test_info in enumerate(test_scripts, 1):
        print(f"\n进度: {i}/{len(test_scripts)}")
        
        try:
            success, error = run_test_script(
                test_info['script'],
                test_info['name'],
                test_info['timeout']
            )
            
            test_results.append({
                'name': test_info['name'],
                'script': test_info['script'],
                'success': success,
                'error': error,
                'critical': test_info['critical']
            })
            
        except KeyboardInterrupt:
            print(f"\n\n用户中断测试")
            break
    
    end_time = time.time()
    total_duration = end_time - start_time
    
    # 生成最终报告
    print(f"\n{'='*80}")
    print("最终测试报告")
    print(f"{'='*80}")
    
    total_tests = len(test_results)
    passed_tests = sum(1 for r in test_results if r['success'])
    failed_tests = total_tests - passed_tests
    critical_failed = sum(1 for r in test_results if not r['success'] and r['critical'])
    
    print(f"测试执行时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"总耗时: {total_duration:.1f}秒")
    print(f"总测试数: {total_tests}")
    print(f"通过: {passed_tests}")
    print(f"失败: {failed_tests}")
    print(f"关键测试失败: {critical_failed}")
    print(f"成功率: {(passed_tests/total_tests*100):.1f}%")
    
    print(f"\n详细结果:")
    for result in test_results:
        status = "[PASS]" if result['success'] else "[FAIL]"
        critical = " (关键)" if result['critical'] else ""
        name = result['name']
        
        print(f"  {status} {name}{critical}")
        
        if not result['success'] and result['error']:
            # 只显示错误的前两行
            error_lines = result['error'].split('\n')[:2]
            for line in error_lines:
                if line.strip():
                    print(f"    错误: {line.strip()}")
    
    # 最终结论
    print(f"\n最终结论:")
    if failed_tests == 0:
        print("🎉 [SUCCESS] 所有测试通过！")
        print("   - OCR功能完全正常")
        print("   - 系统已准备好投入使用")
        print("   - 可以开始处理OCR任务")
        final_status = 0
    elif critical_failed == 0:
        print("✅ [GOOD] 关键测试通过！")
        print("   - OCR核心功能正常")
        print("   - 系统基本可用")
        print("   - 建议解决非关键问题")
        final_status = 0
    elif passed_tests > failed_tests:
        print("⚠️  [WARNING] 大部分测试通过")
        print("   - 系统部分可用")
        print("   - 需要解决关键问题")
        final_status = 1
    else:
        print("❌ [ERROR] 多数测试失败")
        print("   - 系统存在严重问题")
        print("   - 需要全面检查和修复")
        final_status = 2
    
    # 建议
    print(f"\n建议:")
    if failed_tests == 0:
        print("- 系统已准备好进行生产使用")
        print("- 可以开始处理真实的OCR任务")
        print("- 建议定期运行测试以确保系统稳定")
    else:
        print("- 检查失败测试的详细错误信息")
        print("- 确认所有依赖包已正确安装")
        print("- 检查测试文件是否存在且完整")
        if critical_failed > 0:
            print("- 优先解决关键测试失败问题")
    
    # 相关文件
    print(f"\n相关文件:")
    print(f"- 详细测试报告: tests/OCR_COMPREHENSIVE_TEST_REPORT.md")
    print(f"- 测试脚本目录: tests/")
    print(f"- 项目根目录: {project_root}")
    
    return final_status


if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n测试套件被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试套件运行异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
