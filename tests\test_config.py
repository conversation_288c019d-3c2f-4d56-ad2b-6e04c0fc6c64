#!/usr/bin/env python3
"""
配置系统测试脚本
"""

import sys
import os
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def test_app_settings():
    """测试应用设置"""
    print("测试应用设置...")
    
    try:
        from config.settings import AppSettings, OCRSettings, TranslationSettings, OutputSettings, UISettings
        
        # 测试默认设置创建
        settings = AppSettings()
        print("✅ 默认设置创建成功")
        
        # 测试各个子设置
        print(f"  - OCR语言: {settings.ocr.lang}")
        print(f"  - 使用GPU: {settings.ocr.use_gpu}")
        print(f"  - 界面主题: {settings.ui.theme}")
        print(f"  - 输出格式: {settings.output.default_format}")
        
        # 测试设置修改
        settings.ocr.lang = 'en'
        settings.ocr.use_gpu = False
        print("✅ 设置修改成功")
        
        # 测试设置验证
        assert settings.ocr.lang == 'en'
        assert settings.ocr.use_gpu == False
        print("✅ 设置验证成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 应用设置测试失败: {e}")
        return False


def test_settings_persistence():
    """测试设置持久化"""
    print("\n测试设置持久化...")
    
    try:
        from config.settings import AppSettings
        
        # 创建临时配置文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
            temp_config_path = Path(f.name)
        
        try:
            # 创建设置并保存
            settings1 = AppSettings()
            settings1.ocr.lang = 'en'
            settings1.ocr.use_gpu = False
            settings1.ui.theme = 'dark'
            
            # 保存到临时文件
            settings1.save_to_file(temp_config_path)
            print("✅ 设置保存成功")
            
            # 从文件加载设置
            settings2 = AppSettings.load_from_file(temp_config_path)
            print("✅ 设置加载成功")
            
            # 验证加载的设置
            assert settings2.ocr.lang == 'en'
            assert settings2.ocr.use_gpu == False
            assert settings2.ui.theme == 'dark'
            print("✅ 设置持久化验证成功")
            
            return True
            
        finally:
            # 清理临时文件
            if temp_config_path.exists():
                temp_config_path.unlink()
        
    except Exception as e:
        print(f"❌ 设置持久化测试失败: {e}")
        return False


def test_models_config():
    """测试模型配置"""
    print("\n测试模型配置...")
    
    try:
        from config.models_config import ModelsConfig
        
        # 创建模型配置
        models_config = ModelsConfig()
        print("✅ 模型配置创建成功")
        
        # 测试获取所有模型
        all_models = models_config.get_all_models()
        print(f"✅ 获取所有模型成功，共 {len(all_models)} 个模型")
        
        # 显示模型信息
        for model_type, models in all_models.items():
            print(f"  - {model_type}: {len(models)} 个模型")
        
        # 测试检查缺失模型
        missing_models = models_config.get_missing_models()
        if missing_models:
            print(f"  - 缺少 {len(missing_models)} 个模型文件")
        else:
            print("  - 所有模型文件已存在")
        
        # 测试计算下载大小
        total_size = models_config.get_total_download_size()
        print(f"  - 总下载大小: {total_size:.1f} MB")
        
        # 测试特定模型检查
        det_model_path = models_config.get_model_path('det', 'ch_PP-OCRv4_det')
        print(f"✅ 检测模型路径: {det_model_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型配置测试失败: {e}")
        return False


def test_config_validation():
    """测试配置验证"""
    print("\n测试配置验证...")
    
    try:
        from config.settings import AppSettings
        
        settings = AppSettings()
        
        # 测试有效值
        valid_langs = ['ch', 'en', 'fr', 'german', 'korean', 'japan']
        for lang in valid_langs:
            settings.ocr.lang = lang
            print(f"✅ 语言设置 '{lang}' 有效")
        
        # 测试主题设置
        valid_themes = ['light', 'dark', 'auto']
        for theme in valid_themes:
            settings.ui.theme = theme
            print(f"✅ 主题设置 '{theme}' 有效")
        
        # 测试输出格式
        valid_formats = ['txt', 'docx', 'pdf', 'xlsx']
        for fmt in valid_formats:
            settings.output.default_format = fmt
            print(f"✅ 输出格式 '{fmt}' 有效")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置验证测试失败: {e}")
        return False


def test_config_directory_creation():
    """测试配置目录创建"""
    print("\n测试配置目录创建...")
    
    try:
        from config.settings import AppSettings
        
        # 测试获取配置目录
        config_dir = AppSettings.get_config_directory()
        print(f"✅ 配置目录: {config_dir}")
        
        # 检查目录是否存在或可以创建
        if not config_dir.exists():
            config_dir.mkdir(parents=True, exist_ok=True)
            print("✅ 配置目录创建成功")
        else:
            print("✅ 配置目录已存在")
        
        # 测试配置文件路径
        config_file = AppSettings.get_config_file_path()
        print(f"✅ 配置文件路径: {config_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置目录测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("Prisma OCR & Translator - 配置系统测试")
    print("=" * 50)
    
    tests = [
        ("应用设置", test_app_settings),
        ("设置持久化", test_settings_persistence),
        ("模型配置", test_models_config),
        ("配置验证", test_config_validation),
        ("配置目录", test_config_directory_creation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有配置测试通过！")
        return 0
    else:
        print("⚠️  部分配置测试失败，请检查相关问题。")
        return 1


if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程发生未知错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
