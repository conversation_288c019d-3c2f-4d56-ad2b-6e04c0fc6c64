#!/usr/bin/env python3
"""
测试WebView版本的基本功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        # 测试webui模块
        from webui.api_bridge import APIBridge
        from webui.web_controller import WebController
        from webui.file_api import FileAPI
        from webui.ocr_api import OCRAPI
        print("✓ WebUI模块导入成功")
        
        # 测试配置模块
        from config.settings import AppSettings
        from config.models_config import ModelsConfig
        print("✓ 配置模块导入成功")
        
        # 测试工具模块
        from utils.file_utils import FileUtils
        from utils.system_monitor import SystemMonitor
        print("✓ 工具模块导入成功")
        
        # 测试核心模块
        from core.ocr_engine import OCREngine
        print("✓ 核心模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"✗ 模块导入失败: {e}")
        return False


def test_api_bridge():
    """测试API桥接"""
    print("\n测试API桥接...")
    
    try:
        from webui.api_bridge import APIBridge
        
        # 创建API桥接实例
        api_bridge = APIBridge()
        print("✓ API桥接创建成功")
        
        # 测试获取系统资源
        resources = api_bridge.get_system_resources()
        print(f"✓ 系统资源获取成功: CPU {resources.get('cpu_percent', 0):.1f}%")
        
        # 测试获取设置
        settings = api_bridge.get_settings()
        print(f"✓ 设置获取成功: GPU使用 {settings['ocr']['use_gpu']}")
        
        # 清理
        api_bridge.cleanup()
        print("✓ API桥接清理成功")
        
        return True
        
    except Exception as e:
        print(f"✗ API桥接测试失败: {e}")
        return False


def test_file_api():
    """测试文件API"""
    print("\n测试文件API...")
    
    try:
        from webui.file_api import FileAPI
        
        # 创建文件API实例
        file_api = FileAPI()
        print("✓ 文件API创建成功")
        
        # 测试获取支持的格式
        formats = file_api.get_supported_formats()
        print(f"✓ 支持的格式获取成功: {len(formats['input_formats']['images'])} 种图像格式")
        
        # 测试文件验证（使用不存在的文件）
        result = file_api.validate_file("nonexistent.pdf")
        if not result['valid']:
            print("✓ 文件验证功能正常")
        
        return True
        
    except Exception as e:
        print(f"✗ 文件API测试失败: {e}")
        return False


def test_system_monitor():
    """测试系统监控"""
    print("\n测试系统监控...")
    
    try:
        from utils.system_monitor import SystemMonitor
        import time
        
        # 创建系统监控实例
        monitor = SystemMonitor()
        print("✓ 系统监控创建成功")
        
        # 测试获取系统资源
        resources = monitor.get_system_resources()
        print(f"✓ 系统资源: CPU {resources.get('cpu_percent', 0):.1f}%, 内存 {resources.get('memory_percent', 0):.1f}%")
        
        # 测试监控启动和停止
        monitor.start_monitoring()
        print("✓ 监控启动成功")
        
        time.sleep(1)  # 等待一秒
        
        monitor.stop_monitoring()
        print("✓ 监控停止成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 系统监控测试失败: {e}")
        return False


def test_dependencies():
    """测试依赖检查"""
    print("\n测试依赖检查...")
    
    try:
        import webview
        # pywebview 6.0+ 可能没有 __version__ 属性
        version = getattr(webview, '__version__', '6.0+')
        print(f"✓ pywebview 版本: {version}")
    except ImportError:
        print("✗ pywebview 未安装")
        return False
    
    try:
        import paddle
        print(f"✓ paddlepaddle 已安装")
    except ImportError:
        print("✗ paddlepaddle 未安装")
        return False
    
    try:
        import paddleocr
        print(f"✓ paddleocr 已安装")
    except ImportError:
        print("✗ paddleocr 未安装")
        return False
    
    return True


def main():
    """主测试函数"""
    print("Prisma OCR WebView版本 - 功能测试")
    print("=" * 50)
    
    tests = [
        ("依赖检查", test_dependencies),
        ("模块导入", test_imports),
        ("API桥接", test_api_bridge),
        ("文件API", test_file_api),
        ("系统监控", test_system_monitor),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n[{passed + 1}/{total}] {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！WebView版本准备就绪。")
        return True
    else:
        print("❌ 部分测试失败，请检查相关问题。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
