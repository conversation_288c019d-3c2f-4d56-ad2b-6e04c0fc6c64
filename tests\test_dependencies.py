#!/usr/bin/env python3
"""
依赖包检查测试脚本
"""

import sys
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    if sys.version_info < (3, 8):
        print(f"❌ Python版本过低: {sys.version}")
        print("需要Python 3.8或更高版本")
        return False
    else:
        print(f"✅ Python版本: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
        return True


def check_pip():
    """检查pip是否可用"""
    print("\n检查pip...")
    try:
        result = subprocess.run([sys.executable, "-m", "pip", "--version"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ pip可用: {result.stdout.strip()}")
            return True
        else:
            print("❌ pip不可用")
            return False
    except Exception as e:
        print(f"❌ pip检查失败: {e}")
        return False


def check_required_packages():
    """检查必需的包"""
    print("\n检查必需的包...")
    
    required_packages = [
        ("numpy", "数值计算库"),
        ("Pillow", "图像处理库"),
        ("opencv-python", "计算机视觉库"),
        ("pdf2image", "PDF转图像库"),
        ("psutil", "系统监控库"),
        ("toml", "配置文件解析库"),
        ("tqdm", "进度条库"),
    ]
    
    missing_packages = []
    
    for package, description in required_packages:
        try:
            __import__(package.replace('-', '_').lower())
            print(f"✅ {package} - {description}")
        except ImportError:
            print(f"❌ {package} - {description} (未安装)")
            missing_packages.append(package)
    
    return missing_packages


def check_optional_packages():
    """检查可选的包"""
    print("\n检查可选的包...")
    
    optional_packages = [
        ("webview", "Web GUI框架", "界面显示"),
        ("paddlepaddle", "深度学习框架", "OCR核心"),
        ("paddleocr", "OCR引擎", "文字识别"),
        ("paddlenlp", "自然语言处理", "翻译功能"),
        ("pynvml", "NVIDIA GPU监控", "GPU状态监控"),
        ("openpyxl", "Excel文件处理", "Excel输出"),
        ("docx", "Word文档处理", "Word输出"),
        ("reportlab", "PDF生成", "PDF输出"),
    ]
    
    available_packages = []
    missing_packages = []
    
    for package, description, purpose in optional_packages:
        try:
            __import__(package.replace('-', '_').lower())
            print(f"✅ {package} - {description} ({purpose})")
            available_packages.append(package)
        except ImportError:
            print(f"⚠️  {package} - {description} ({purpose}) (未安装)")
            missing_packages.append(package)
    
    return available_packages, missing_packages


def check_system_requirements():
    """检查系统要求"""
    print("\n检查系统要求...")
    
    try:
        import psutil
        
        # 检查内存
        memory = psutil.virtual_memory()
        memory_gb = memory.total / (1024**3)
        print(f"✅ 系统内存: {memory_gb:.1f} GB")
        
        if memory_gb < 4:
            print("⚠️  建议至少4GB内存以获得更好性能")
        
        # 检查磁盘空间
        disk = psutil.disk_usage('.')
        free_gb = disk.free / (1024**3)
        print(f"✅ 可用磁盘空间: {free_gb:.1f} GB")
        
        if free_gb < 2:
            print("⚠️  建议至少2GB可用磁盘空间用于模型文件")
        
        # 检查CPU
        cpu_count = psutil.cpu_count()
        print(f"✅ CPU核心数: {cpu_count}")
        
        return True
        
    except ImportError:
        print("❌ 无法检查系统要求 (psutil未安装)")
        return False
    except Exception as e:
        print(f"❌ 系统要求检查失败: {e}")
        return False


def check_gpu_support():
    """检查GPU支持"""
    print("\n检查GPU支持...")
    
    try:
        import pynvml
        pynvml.nvmlInit()
        device_count = pynvml.nvmlDeviceGetCount()
        
        if device_count > 0:
            for i in range(device_count):
                handle = pynvml.nvmlDeviceGetHandleByIndex(i)
                name = pynvml.nvmlDeviceGetName(handle).decode('utf-8')
                print(f"✅ 检测到GPU {i}: {name}")
            
            # 检查CUDA支持
            try:
                import paddle
                if paddle.is_compiled_with_cuda():
                    print("✅ PaddlePaddle支持CUDA")
                else:
                    print("⚠️  PaddlePaddle不支持CUDA (将使用CPU模式)")
            except ImportError:
                print("⚠️  PaddlePaddle未安装，无法检查CUDA支持")
            
            return True
        else:
            print("⚠️  未检测到NVIDIA GPU")
            return False
            
    except ImportError:
        print("⚠️  pynvml未安装，无法检查GPU")
        return False
    except Exception as e:
        print(f"⚠️  GPU检查失败: {e}")
        return False


def generate_install_commands(missing_required, missing_optional):
    """生成安装命令"""
    print("\n" + "="*50)
    print("安装建议:")
    
    if missing_required:
        print("\n必需包安装命令:")
        print(f"pip install {' '.join(missing_required)}")
    
    if missing_optional:
        print("\n可选包安装命令:")
        print("# 基础GUI和OCR功能")
        basic_packages = [p for p in missing_optional if p in ['pywebview', 'paddlepaddle', 'paddleocr']]
        if basic_packages:
            print(f"pip install {' '.join(basic_packages)}")
        
        print("\n# 完整功能包")
        print(f"pip install {' '.join(missing_optional)}")
        
        print("\n# 或者安装所有依赖")
        print("pip install -r requirements.txt")


def main():
    """主测试函数"""
    print("Prisma OCR & Translator - 依赖检查")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        return 1
    
    # 检查pip
    if not check_pip():
        return 1
    
    # 检查必需包
    missing_required = check_required_packages()
    
    # 检查可选包
    available_optional, missing_optional = check_optional_packages()
    
    # 检查系统要求
    check_system_requirements()
    
    # 检查GPU支持
    check_gpu_support()
    
    # 生成安装建议
    if missing_required or missing_optional:
        generate_install_commands(missing_required, missing_optional)
    
    # 总结
    print("\n" + "="*50)
    print("检查总结:")
    
    if not missing_required:
        print("✅ 所有必需包已安装")
    else:
        print(f"❌ 缺少 {len(missing_required)} 个必需包")
    
    if len(available_optional) > len(missing_optional):
        print(f"✅ 大部分可选包已安装 ({len(available_optional)}/{len(available_optional) + len(missing_optional)})")
    else:
        print(f"⚠️  多数可选包未安装 ({len(available_optional)}/{len(available_optional) + len(missing_optional)})")
    
    if not missing_required and len(available_optional) >= 3:
        print("🎉 环境配置良好，可以运行基本功能")
        return 0
    else:
        print("⚠️  建议安装缺少的包以获得完整功能")
        return 1


if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n检查被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n检查过程发生未知错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
