#!/usr/bin/env python3
"""
PDF处理专项测试
使用项目根目录下的ocr测试.pdf进行完整的OCR处理测试
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def check_test_file():
    """检查测试文件是否存在"""
    test_pdf = project_root / "ocr测试.pdf"
    if not test_pdf.exists():
        print(f"❌ 测试文件不存在: {test_pdf}")
        return None
    
    # 获取文件信息
    file_size = test_pdf.stat().st_size / (1024 * 1024)  # MB
    print(f"✅ 测试文件: {test_pdf.name}")
    print(f"   文件大小: {file_size:.2f} MB")
    
    return test_pdf


def test_minimal_dependencies():
    """测试最小依赖"""
    print("\n检查最小依赖...")
    
    required_modules = [
        ("pathlib", "路径处理"),
        ("sys", "系统接口"),
        ("os", "操作系统接口"),
        ("time", "时间处理"),
    ]
    
    for module, desc in required_modules:
        try:
            __import__(module)
            print(f"✅ {module} - {desc}")
        except ImportError:
            print(f"❌ {module} - {desc}")
            return False
    
    return True


def test_image_processing_dependencies():
    """测试图像处理依赖"""
    print("\n检查图像处理依赖...")
    
    try:
        import numpy as np
        print(f"✅ NumPy {np.__version__}")
    except ImportError:
        print("❌ NumPy 未安装")
        return False
    
    try:
        from PIL import Image
        print(f"✅ Pillow (PIL)")
    except ImportError:
        print("❌ Pillow 未安装")
        return False
    
    try:
        import cv2
        print(f"✅ OpenCV {cv2.__version__}")
    except ImportError:
        print("❌ OpenCV 未安装")
        return False
    
    try:
        import pdf2image
        print("✅ pdf2image")
    except ImportError:
        print("❌ pdf2image 未安装")
        return False
    
    return True


def test_pdf_to_image_conversion(pdf_path):
    """测试PDF转图像功能"""
    print(f"\n测试PDF转图像转换...")
    
    try:
        from utils.image_utils import ImageUtils
        
        print("正在转换PDF...")
        start_time = time.time()
        
        # 使用较低的DPI以加快测试速度
        images = ImageUtils.convert_pdf_to_images(pdf_path, dpi=150)
        
        conversion_time = time.time() - start_time
        
        print(f"✅ 转换完成")
        print(f"   页数: {len(images)}")
        print(f"   耗时: {conversion_time:.2f}秒")
        
        # 显示每页信息
        for i, img in enumerate(images):
            print(f"   页面 {i+1}: {img.size[0]}x{img.size[1]} 像素, 模式: {img.mode}")
        
        return images
        
    except Exception as e:
        print(f"❌ PDF转换失败: {e}")
        return None


def test_image_preprocessing(images):
    """测试图像预处理"""
    print(f"\n测试图像预处理...")
    
    if not images:
        print("❌ 没有图像可供测试")
        return False
    
    try:
        from utils.image_utils import ImageUtils
        
        # 测试第一页
        first_image = images[0]
        print(f"原始图像: {first_image.size}, {first_image.mode}")
        
        # 转换为OpenCV格式
        cv_image = ImageUtils.pil_to_cv2(first_image)
        print(f"✅ PIL转OpenCV: {cv_image.shape}")
        
        # 转换回PIL格式
        pil_image = ImageUtils.cv2_to_pil(cv_image)
        print(f"✅ OpenCV转PIL: {pil_image.size}")
        
        # 测试图像增强（如果有的话）
        try:
            enhanced_image = ImageUtils.enhance_for_ocr(cv_image)
            print(f"✅ 图像增强: {enhanced_image.shape}")
        except AttributeError:
            print("⚠️  图像增强功能未实现")
        
        return True
        
    except Exception as e:
        print(f"❌ 图像预处理失败: {e}")
        return False


def test_basic_ocr(images):
    """测试基础OCR功能"""
    print(f"\n测试基础OCR功能...")
    
    if not images:
        print("❌ 没有图像可供测试")
        return False
    
    # 检查PaddleOCR依赖
    try:
        import paddle
        import paddleocr
        print("✅ PaddleOCR依赖可用")
    except ImportError as e:
        print(f"❌ PaddleOCR依赖不可用: {e}")
        print("请安装: pip install paddlepaddle paddleocr")
        return False
    
    try:
        from paddleocr import PaddleOCR
        from utils.image_utils import ImageUtils
        
        # 初始化OCR（使用CPU模式确保兼容性）
        print("初始化OCR引擎...")
        ocr = PaddleOCR(
            use_angle_cls=True,
            lang='ch',
            show_log=False,
            use_gpu=False
        )
        print("✅ OCR引擎初始化完成")
        
        # 只测试第一页以节省时间
        test_image = images[0]
        cv_image = ImageUtils.pil_to_cv2(test_image)
        
        print("执行OCR识别...")
        start_time = time.time()
        result = ocr.ocr(cv_image, cls=True)
        ocr_time = time.time() - start_time
        
        print(f"✅ OCR识别完成，耗时: {ocr_time:.2f}秒")
        
        # 分析结果
        if result and result[0]:
            text_blocks = result[0]
            print(f"   识别到 {len(text_blocks)} 个文本块")
            
            # 显示前几个结果
            for i, block in enumerate(text_blocks[:3]):
                if len(block) >= 2:
                    text = block[1][0] if isinstance(block[1], (list, tuple)) else str(block[1])
                    confidence = block[1][1] if isinstance(block[1], (list, tuple)) and len(block[1]) > 1 else 0.0
                    print(f"   {i+1}. {text[:50]}... (置信度: {confidence:.2f})")
            
            if len(text_blocks) > 3:
                print(f"   ... 还有 {len(text_blocks) - 3} 个文本块")
            
            return True
        else:
            print("⚠️  未识别到任何文本")
            return False
            
    except Exception as e:
        print(f"❌ OCR测试失败: {e}")
        return False


def test_output_generation(pdf_path):
    """测试输出生成"""
    print(f"\n测试输出生成...")
    
    try:
        from utils.file_utils import FileUtils
        
        # 测试输出文件名生成
        txt_output = FileUtils.get_output_filename(pdf_path, 'txt')
        print(f"✅ TXT输出文件名: {txt_output}")
        
        docx_output = FileUtils.get_output_filename(pdf_path, 'docx')
        print(f"✅ DOCX输出文件名: {docx_output}")
        
        # 测试创建简单的文本输出
        test_content = "这是一个测试输出文件\n包含OCR识别的结果"
        
        with open(txt_output, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        if txt_output.exists():
            print(f"✅ 测试输出文件创建成功: {txt_output}")
            # 清理测试文件
            txt_output.unlink()
            return True
        else:
            print("❌ 测试输出文件创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 输出生成测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("Prisma OCR - PDF处理专项测试")
    print("=" * 50)
    
    # 检查测试文件
    pdf_path = check_test_file()
    if not pdf_path:
        return 1
    
    # 测试步骤
    tests = [
        ("最小依赖", lambda: test_minimal_dependencies()),
        ("图像处理依赖", lambda: test_image_processing_dependencies()),
        ("PDF转图像", lambda: test_pdf_to_image_conversion(pdf_path)),
        ("图像预处理", lambda: test_image_preprocessing(test_pdf_to_image_conversion(pdf_path))),
        ("基础OCR", lambda: test_basic_ocr(test_pdf_to_image_conversion(pdf_path))),
        ("输出生成", lambda: test_output_generation(pdf_path)),
    ]
    
    passed = 0
    total = len(tests)
    images = None
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            # 特殊处理需要图像的测试
            if test_name in ["图像预处理", "基础OCR"] and images is None:
                images = test_pdf_to_image_conversion(pdf_path)
            
            if test_name == "图像预处理":
                result = test_image_preprocessing(images)
            elif test_name == "基础OCR":
                result = test_basic_ocr(images)
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
                
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    # 总结
    print(f"\n{'='*50}")
    print(f"PDF处理测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有PDF处理测试通过！")
        print("可以使用 'python tests/process_pdf.py' 进行完整处理")
        return 0
    elif passed >= total // 2:
        print("⚠️  部分测试通过，基本功能可用")
        return 1
    else:
        print("❌ 多数测试失败，请检查环境配置")
        return 2


if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\nPDF处理测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\nPDF处理测试异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
