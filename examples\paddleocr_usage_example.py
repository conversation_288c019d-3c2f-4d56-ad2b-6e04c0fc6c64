#!/usr/bin/env python3
"""
PaddleOCR 全功能版本使用示例
展示如何使用安装的 PaddleOCR 进行各种OCR任务
"""

import os
import sys
from pathlib import Path

def basic_ocr_example():
    """基础OCR识别示例"""
    print("=" * 50)
    print("基础OCR识别示例")
    print("=" * 50)
    
    try:
        import paddleocr
        
        # 初始化OCR
        ocr = paddleocr.PaddleOCR(
            use_textline_orientation=True,  # 使用文本行方向分类
            lang='ch'                       # 中文识别
        )
        
        # 查找测试图片
        test_images = [
            "ocr测试.PNG",
            "tests/ocr测试.PNG", 
            "images/test.jpg"
        ]
        
        test_image = None
        for img_path in test_images:
            if Path(img_path).exists():
                test_image = img_path
                break
        
        if test_image:
            print(f"使用测试图片: {test_image}")
            
            # 执行OCR识别
            result = ocr.ocr(test_image, cls=True)
            
            if result and len(result) > 0:
                print(f"\n识别结果 (共 {len(result[0])} 个文本区域):")
                for idx, line in enumerate(result[0]):
                    bbox, (text, confidence) = line
                    print(f"{idx+1}. 文本: {text}")
                    print(f"   置信度: {confidence:.4f}")
                    print(f"   位置: {bbox}")
                    print()
            else:
                print("未识别到文本内容")
        else:
            print("未找到测试图片，请将图片放在以下位置之一:")
            for img_path in test_images:
                print(f"  - {img_path}")
                
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保已正确安装 PaddleOCR")
    except Exception as e:
        print(f"执行错误: {e}")

def multilingual_ocr_example():
    """多语言OCR示例"""
    print("=" * 50)
    print("多语言OCR示例")
    print("=" * 50)
    
    try:
        import paddleocr
        
        # 支持的语言列表
        languages = ['ch', 'en', 'fr', 'german', 'korean', 'japan']
        
        print("PaddleOCR 支持的部分语言:")
        for lang in languages:
            print(f"  - {lang}")
        
        # 示例：英文OCR
        print("\n初始化英文OCR...")
        ocr_en = paddleocr.PaddleOCR(use_textline_orientation=True, lang='en')
        print("✅ 英文OCR初始化成功")

        # 示例：中英文混合OCR
        print("\n初始化中英文混合OCR...")
        ocr_ch = paddleocr.PaddleOCR(use_textline_orientation=True, lang='ch')
        print("✅ 中英文混合OCR初始化成功")
        
    except Exception as e:
        print(f"多语言OCR示例执行错误: {e}")

def document_analysis_example():
    """文档分析示例（全功能版本特性）"""
    print("=" * 50)
    print("文档分析示例（全功能版本特性）")
    print("=" * 50)

    try:
        import paddleocr

        print("PaddleOCR 全功能版本包含以下高级功能:")
        print("1. 文档解析 (doc-parser)")
        print("   - 表格识别和提取")
        print("   - 公式识别")
        print("   - 印章检测")
        print("   - 版面分析")

        print("\n2. 信息抽取 (ie)")
        print("   - 关键信息提取")
        print("   - 智能文档理解")
        print("   - 结构化数据输出")

        print("\n3. 文档翻译 (trans)")
        print("   - 多语言文档翻译")
        print("   - 保持原始格式")
        print("   - 批量处理")

        # 注意：具体的高级功能使用需要参考PaddleOCR官方文档
        print("\n注意：具体的高级功能使用方法请参考:")
        print("- PaddleOCR官方文档: https://paddleocr.readthedocs.io/")
        print("- 项目文档: docs/OCR_INSTALLATION_GUIDE.md")
        print("- PP-StructureV3文档: docs/PP_STRUCTUREV3_USAGE_GUIDE.md")

    except Exception as e:
        print(f"文档分析示例执行错误: {e}")

def pp_doc_translation_example():
    """PP-DocTranslation使用示例"""
    print("=" * 50)
    print("PP-DocTranslation使用示例")
    print("=" * 50)

    try:
        # 尝试导入PP-DocTranslation
        try:
            from paddleocr import PPDocTranslation
            print("✅ PP-DocTranslation导入成功")
        except ImportError:
            print("❌ PP-DocTranslation不可用，请安装完整版PaddleOCR")
            print("安装命令: python -m pip install \"paddleocr[all]\"")
            return

        print("\n初始化PP-DocTranslation产线...")
        pipeline = PPDocTranslation(
            use_doc_orientation_classify=False,  # 文档方向分类
            use_doc_unwarping=False,            # 文档扭曲矫正
            use_textline_orientation=True,      # 文本行方向分类
            use_seal_recognition=True,          # 启用印章识别
            use_table_recognition=True,         # 启用表格识别
            use_formula_recognition=True,       # 启用公式识别
            use_chart_recognition=False,        # 禁用图表解析
            use_region_detection=True,          # 启用版面区域检测
            device='cpu'                        # 使用CPU
        )
        print("✅ PP-DocTranslation初始化成功")

        # 查找测试图片
        test_images = [
            "ocr测试.PNG",
            "tests/ocr测试.PNG",
            "images/test.jpg"
        ]

        test_image = None
        for img_path in test_images:
            if Path(img_path).exists():
                test_image = img_path
                break

        if test_image:
            print(f"\n使用测试图片: {test_image}")
            print("正在执行PP-DocTranslation视觉预测...")

            # 执行视觉预测
            results = pipeline.visual_predict(test_image)

            if results:
                print("✅ PP-DocTranslation视觉预测完成")

                for i, result in enumerate(results):
                    print(f"\n--- 结果 {i+1} ---")
                    layout_parsing_result = result.get("layout_parsing_result")

                    if layout_parsing_result:
                        # 获取结果数据
                        res_data = layout_parsing_result.res if hasattr(layout_parsing_result, 'res') else {}

                        # 分析解析结果
                        parsing_results = res_data.get('parsing_res_list', [])
                        print(f"检测到 {len(parsing_results)} 个版面区域")

                        # 分析表格结果
                        table_results = res_data.get('table_res_list', [])
                        if table_results:
                            print(f"检测到 {len(table_results)} 个表格")

                        # 分析公式结果
                        formula_results = res_data.get('formula_res_list', [])
                        if formula_results:
                            print(f"检测到 {len(formula_results)} 个公式")

                        # 分析印章结果
                        seal_results = res_data.get('seal_res_list', [])
                        if seal_results:
                            print(f"检测到 {len(seal_results)} 个印章")

                print(f"🎉 PP-DocTranslation示例运行成功！")
            else:
                print("⚠️  PP-DocTranslation未返回结果")
        else:
            print("⚠️  未找到测试图片，跳过PP-DocTranslation功能测试")
            print("支持的功能包括:")
            print("- 版面区域检测")
            print("- 文本识别")
            print("- 表格识别")
            print("- 公式识别")
            print("- 印章识别")
            print("- 多格式输出（JSON、Markdown、HTML、Excel）")
            print("- 注意：项目中不使用文档翻译功能，仅使用视觉识别功能")

    except Exception as e:
        print(f"PP-DocTranslation示例执行错误: {e}")
        import traceback
        traceback.print_exc()


def pp_structure_v3_example():
    """PP-StructureV3使用示例"""
    print("=" * 50)
    print("PP-StructureV3使用示例")
    print("=" * 50)

    try:
        # 尝试导入PP-StructureV3
        try:
            from paddleocr import PPStructureV3
            print("✅ PP-StructureV3导入成功")
        except ImportError:
            print("❌ PP-StructureV3不可用，请安装完整版PaddleOCR")
            print("安装命令: python -m pip install \"paddleocr[all]\"")
            return

        print("\n初始化PP-StructureV3产线...")
        pipeline = PPStructureV3(
            use_seal_recognition=True,      # 启用印章识别
            use_table_recognition=True,     # 启用表格识别
            use_formula_recognition=True,   # 启用公式识别
            use_chart_recognition=False,    # 禁用图表解析
            use_region_detection=True,      # 启用版面区域检测
            device='cpu'                    # 使用CPU
        )
        print("✅ PP-StructureV3初始化成功")

        # 查找测试图片
        test_images = [
            "ocr测试.PNG",
            "tests/ocr测试.PNG",
            "images/test.jpg"
        ]

        test_image = None
        for img_path in test_images:
            if Path(img_path).exists():
                test_image = img_path
                break

        if test_image:
            print(f"\n使用测试图片: {test_image}")
            print("正在执行PP-StructureV3分析...")

            # 执行预测
            results = pipeline.predict(test_image)

            if results:
                print("✅ PP-StructureV3分析完成")

                for i, result in enumerate(results):
                    print(f"\n--- 第 {i+1} 页结果 ---")

                    # 获取JSON结果
                    json_data = result.json

                    # 分析版面区域
                    parsing_results = json_data.get('parsing_res_list', [])
                    print(f"检测到 {len(parsing_results)} 个版面区域:")

                    for j, block in enumerate(parsing_results[:3]):  # 只显示前3个
                        block_type = block.get('block_label', 'unknown')
                        block_content = block.get('block_content', '')
                        print(f"  {j+1}. {block_type}: {block_content[:30]}...")

                    # 分析表格
                    table_results = json_data.get('table_res_list', [])
                    if table_results:
                        print(f"检测到 {len(table_results)} 个表格")

                    # 分析公式
                    formula_results = json_data.get('formula_res_list', [])
                    if formula_results:
                        print(f"检测到 {len(formula_results)} 个公式")

                    # 分析印章
                    seal_results = json_data.get('seal_res_list', [])
                    if seal_results:
                        print(f"检测到 {len(seal_results)} 个印章")

                    print(f"🎉 PP-StructureV3示例运行成功！")
            else:
                print("⚠️  PP-StructureV3未返回结果")
        else:
            print("⚠️  未找到测试图片，跳过PP-StructureV3功能测试")
            print("支持的功能包括:")
            print("- 版面区域检测")
            print("- 文本识别")
            print("- 表格识别")
            print("- 公式识别")
            print("- 印章识别")
            print("- 多格式输出（JSON、Markdown、HTML、Excel）")

    except Exception as e:
        print(f"PP-StructureV3示例执行错误: {e}")
        import traceback
        traceback.print_exc()

def performance_tips():
    """性能优化提示"""
    print("=" * 50)
    print("性能优化提示")
    print("=" * 50)
    
    print("1. 硬件优化:")
    print("   - 使用多核CPU可提升处理速度")
    print("   - 8GB或更多内存避免频繁磁盘交换")
    print("   - SSD硬盘加快模型加载")
    
    print("\n2. 软件优化:")
    print("   - 选择合适的模型大小")
    print("   - 批量处理多个文件")
    print("   - 缓存常用模型")
    
    print("\n3. 参数调优:")
    print("   - use_angle_cls: 是否使用角度分类器")
    print("   - det_db_thresh: 检测阈值")
    print("   - det_db_box_thresh: 框检测阈值")
    print("   - rec_batch_num: 识别批次大小")

def check_installation():
    """检查安装状态"""
    print("=" * 50)
    print("检查PaddleOCR安装状态")
    print("=" * 50)
    
    try:
        import paddle
        import paddleocr
        
        print(f"✅ PaddlePaddle版本: {paddle.__version__}")
        print("✅ PaddleOCR导入成功")
        
        # 检查模型目录
        home_dir = Path.home()
        paddleocr_dir = home_dir / ".paddleocr"
        
        if paddleocr_dir.exists():
            model_files = list(paddleocr_dir.rglob("*.pdmodel"))
            print(f"✅ 模型目录存在，包含 {len(model_files)} 个模型文件")
        else:
            print("⚠️  模型目录不存在，首次使用时会自动下载")
        
        print("\n安装验证成功！可以正常使用PaddleOCR功能。")
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请运行 python install.py 重新安装")
    except Exception as e:
        print(f"❌ 检查过程出错: {e}")

def main():
    """主函数"""
    print("PaddleOCR 全功能版本使用示例")
    print("=" * 60)
    
    # 检查安装状态
    check_installation()
    
    # 基础OCR示例
    basic_ocr_example()
    
    # 多语言OCR示例
    multilingual_ocr_example()
    
    # 文档分析示例
    document_analysis_example()

    # PP-DocTranslation示例（推荐）
    pp_doc_translation_example()

    # PP-StructureV3示例
    pp_structure_v3_example()

    # 性能优化提示
    performance_tips()
    
    print("\n" + "=" * 60)
    print("示例运行完成！")
    print("更多功能请参考:")
    print("- docs/OCR_INSTALLATION_GUIDE.md")
    print("- PaddleOCR官方文档")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        import traceback
        traceback.print_exc()
