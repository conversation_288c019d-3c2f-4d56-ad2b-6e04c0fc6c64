"""
OCR处理API
处理OCR相关的操作
"""

import logging
import threading
import time
from typing import Dict, Any, List, Optional, Callable
from pathlib import Path

from config.settings import AppSettings
from config.models_config import ModelsConfig
from core.ocr_engine import OCREngine, OCRResult
from utils.file_utils import FileUtils


class OCRAPI:
    """OCR处理API类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 初始化组件
        self.settings = AppSettings()
        self.models_config = ModelsConfig()
        self.ocr_engine: Optional[OCREngine] = None
        
        # 处理状态
        self.is_processing = False
        self.current_progress = 0
        self.current_status = "等待开始..."
        self.processing_error = None
        
        # 结果存储
        self.last_results: List[OCRResult] = []
        
        # 回调函数
        self.progress_callback: Optional[Callable] = None
        self.completion_callback: Optional[Callable] = None
        self.error_callback: Optional[Callable] = None
    
    def initialize_engine(self) -> Dict[str, Any]:
        """
        初始化OCR引擎
        
        Returns:
            初始化结果
        """
        try:
            if self.ocr_engine and self.ocr_engine.is_initialized:
                return {
                    'success': True,
                    'message': 'OCR引擎已初始化'
                }
            
            self.logger.info("开始初始化OCR引擎")
            
            # 创建OCR引擎实例
            self.ocr_engine = OCREngine(self.settings.ocr, self.models_config)
            
            # 设置进度回调
            def progress_update(progress: int, status: str):
                self.current_progress = progress
                self.current_status = status
                if self.progress_callback:
                    self.progress_callback(progress, status)
            
            # 设置错误回调
            def error_occurred(error_msg: str):
                self.processing_error = error_msg
                if self.error_callback:
                    self.error_callback(error_msg)
            
            # 连接信号（这里需要适配非PyQt6环境）
            self.ocr_engine.progress_callback = progress_update
            self.ocr_engine.error_callback = error_occurred
            
            # 初始化引擎
            if self.ocr_engine.initialize():
                self.logger.info("OCR引擎初始化成功")
                return {
                    'success': True,
                    'message': 'OCR引擎初始化成功'
                }
            else:
                return {
                    'success': False,
                    'error': 'OCR引擎初始化失败'
                }
                
        except Exception as e:
            self.logger.error(f"初始化OCR引擎失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def set_processing_mode(self, mode: str) -> Dict[str, Any]:
        """
        设置处理模式
        
        Args:
            mode: 处理模式 ('general' 或 'table')
            
        Returns:
            操作结果
        """
        try:
            if not self.ocr_engine:
                return {
                    'success': False,
                    'error': 'OCR引擎未初始化'
                }
            
            if mode not in ['general', 'table']:
                return {
                    'success': False,
                    'error': f'不支持的处理模式: {mode}'
                }
            
            self.ocr_engine.set_mode(mode)
            
            self.logger.info(f"设置处理模式: {mode}")
            
            return {
                'success': True,
                'message': f'已设置为{mode}模式'
            }
            
        except Exception as e:
            self.logger.error(f"设置处理模式失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def start_processing(self, file_paths: List[str], config: Dict[str, Any]) -> Dict[str, Any]:
        """
        开始处理文件
        
        Args:
            file_paths: 文件路径列表
            config: 处理配置
            
        Returns:
            操作结果
        """
        try:
            if self.is_processing:
                return {
                    'success': False,
                    'error': '正在处理中，请等待完成'
                }
            
            if not file_paths:
                return {
                    'success': False,
                    'error': '文件列表为空'
                }
            
            # 验证文件
            for file_path in file_paths:
                if not Path(file_path).exists():
                    return {
                        'success': False,
                        'error': f'文件不存在: {file_path}'
                    }
                
                if not FileUtils.is_supported_input_file(file_path):
                    return {
                        'success': False,
                        'error': f'不支持的文件格式: {file_path}'
                    }
            
            # 启动处理线程
            processing_thread = threading.Thread(
                target=self._process_files,
                args=(file_paths, config),
                daemon=True
            )
            processing_thread.start()
            
            self.logger.info(f"开始处理 {len(file_paths)} 个文件")
            
            return {
                'success': True,
                'message': f'开始处理 {len(file_paths)} 个文件'
            }
            
        except Exception as e:
            self.logger.error(f"开始处理失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _process_files(self, file_paths: List[str], config: Dict[str, Any]):
        """
        处理文件（在后台线程中运行）
        
        Args:
            file_paths: 文件路径列表
            config: 处理配置
        """
        try:
            self.is_processing = True
            self.current_progress = 0
            self.current_status = "正在初始化..."
            self.processing_error = None
            self.last_results.clear()
            
            # 确保OCR引擎已初始化
            if not self.ocr_engine or not self.ocr_engine.is_initialized:
                init_result = self.initialize_engine()
                if not init_result['success']:
                    raise RuntimeError(init_result['error'])
            
            # 设置处理模式
            mode = config.get('mode', 'general')
            self.ocr_engine.set_mode(mode)
            
            total_files = len(file_paths)
            
            for i, file_path in enumerate(file_paths):
                if not self.is_processing:  # 检查是否被停止
                    break
                
                try:
                    # 更新进度
                    file_progress = int((i / total_files) * 100)
                    self.current_progress = file_progress
                    self.current_status = f"正在处理文件 {i+1}/{total_files}: {Path(file_path).name}"
                    
                    if self.progress_callback:
                        self.progress_callback(file_progress, self.current_status)
                    
                    # 处理单个文件
                    result = self._process_single_file(file_path, config)
                    if result:
                        self.last_results.append(result)
                    
                    # 模拟处理时间
                    time.sleep(0.5)
                    
                except Exception as e:
                    self.logger.error(f"处理文件 {file_path} 失败: {e}")
                    if self.error_callback:
                        self.error_callback(f"处理文件失败: {e}")
            
            if self.is_processing:  # 如果没有被停止
                self.current_progress = 100
                self.current_status = "处理完成"
                
                if self.progress_callback:
                    self.progress_callback(100, "处理完成")
                
                if self.completion_callback:
                    self.completion_callback(self.last_results)
                
                self.logger.info(f"文件处理完成，共处理 {len(self.last_results)} 个文件")
            
        except Exception as e:
            self.logger.error(f"文件处理失败: {e}")
            self.processing_error = str(e)
            
            if self.error_callback:
                self.error_callback(str(e))
        
        finally:
            self.is_processing = False
    
    def _process_single_file(self, file_path: str, config: Dict[str, Any]) -> Optional[OCRResult]:
        """
        处理单个文件
        
        Args:
            file_path: 文件路径
            config: 处理配置
            
        Returns:
            OCR结果
        """
        try:
            # 这里应该调用实际的OCR处理逻辑
            # 目前返回模拟结果
            result = OCRResult()
            result.page_number = 1
            result.add_text_block(
                text=f"模拟OCR结果 - {Path(file_path).name}",
                bbox=[0, 0, 100, 20],
                confidence=0.95
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"处理单个文件失败: {e}")
            return None
    
    def stop_processing(self) -> Dict[str, Any]:
        """
        停止处理
        
        Returns:
            操作结果
        """
        try:
            if not self.is_processing:
                return {
                    'success': False,
                    'error': '当前没有正在处理的任务'
                }
            
            self.is_processing = False
            self.current_progress = 0
            self.current_status = "处理已停止"
            
            self.logger.info("停止OCR处理")
            
            return {
                'success': True,
                'message': '已停止处理'
            }
            
        except Exception as e:
            self.logger.error(f"停止处理失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_processing_status(self) -> Dict[str, Any]:
        """
        获取处理状态
        
        Returns:
            处理状态
        """
        return {
            'is_processing': self.is_processing,
            'progress': self.current_progress,
            'status': self.current_status,
            'error': self.processing_error,
            'results_count': len(self.last_results)
        }
    
    def get_last_results(self) -> List[Dict[str, Any]]:
        """
        获取最后的处理结果
        
        Returns:
            处理结果列表
        """
        results = []
        
        for result in self.last_results:
            result_dict = {
                'page_number': result.page_number,
                'text_blocks': [],
                'tables': result.tables,
                'processing_time': result.processing_time,
                'confidence': result.get_average_confidence()
            }
            
            for text_block in result.text_blocks:
                result_dict['text_blocks'].append({
                    'text': text_block['text'],
                    'bbox': text_block['bbox'],
                    'confidence': text_block['confidence']
                })
            
            results.append(result_dict)
        
        return results
    
    def clear_results(self) -> Dict[str, Any]:
        """
        清空结果
        
        Returns:
            操作结果
        """
        try:
            self.last_results.clear()
            
            self.logger.info("清空OCR结果")
            
            return {
                'success': True,
                'message': '已清空结果'
            }
            
        except Exception as e:
            self.logger.error(f"清空结果失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def set_callbacks(self, progress_callback: Optional[Callable] = None,
                     completion_callback: Optional[Callable] = None,
                     error_callback: Optional[Callable] = None):
        """
        设置回调函数
        
        Args:
            progress_callback: 进度回调函数
            completion_callback: 完成回调函数
            error_callback: 错误回调函数
        """
        self.progress_callback = progress_callback
        self.completion_callback = completion_callback
        self.error_callback = error_callback
