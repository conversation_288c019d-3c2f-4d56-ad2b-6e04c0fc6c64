"""
系统兼容性检查工具
"""

import sys
import platform
import logging
from pathlib import Path
from typing import Dict, List, Any


class CompatibilityChecker:
    """系统兼容性检查器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.issues = []
        self.warnings = []
    
    def check_all(self) -> Dict[str, Any]:
        """执行所有兼容性检查"""
        results = {
            'python_version': self.check_python_version(),
            'platform_info': self.check_platform(),
            'pywebview_compatibility': self.check_pywebview_compatibility(),
            'dependencies': self.check_dependencies(),
            'system_resources': self.check_system_resources(),
            'issues': self.issues,
            'warnings': self.warnings,
            'overall_status': 'unknown'
        }
        
        # 评估整体状态
        if self.issues:
            results['overall_status'] = 'error'
        elif self.warnings:
            results['overall_status'] = 'warning'
        else:
            results['overall_status'] = 'ok'
        
        return results
    
    def check_python_version(self) -> Dict[str, Any]:
        """检查Python版本"""
        version_info = sys.version_info
        version_str = f"{version_info.major}.{version_info.minor}.{version_info.micro}"
        
        result = {
            'version': version_str,
            'major': version_info.major,
            'minor': version_info.minor,
            'micro': version_info.micro,
            'status': 'ok'
        }
        
        if version_info < (3, 8):
            result['status'] = 'error'
            self.issues.append(f"Python版本过低: {version_str} (需要 >= 3.8)")
        elif version_info < (3, 9):
            result['status'] = 'warning'
            self.warnings.append(f"建议升级Python版本: {version_str} (推荐 >= 3.9)")
        
        return result
    
    def check_platform(self) -> Dict[str, Any]:
        """检查操作系统平台"""
        system = platform.system()
        release = platform.release()
        version = platform.version()
        machine = platform.machine()
        
        result = {
            'system': system,
            'release': release,
            'version': version,
            'machine': machine,
            'status': 'ok'
        }
        
        # Windows特定检查
        if system == 'Windows':
            try:
                win_version = platform.win32_ver()
                result['windows_version'] = win_version
                
                # 检查Windows版本兼容性
                if win_version[0] in ['10', '11']:
                    result['webview2_compatible'] = True
                else:
                    result['webview2_compatible'] = False
                    self.warnings.append(f"Windows版本可能不完全支持WebView2: {win_version[0]}")
                    
            except Exception as e:
                self.warnings.append(f"无法获取详细Windows版本信息: {e}")
        
        return result
    
    def check_pywebview_compatibility(self) -> Dict[str, Any]:
        """检查pywebview兼容性"""
        result = {
            'available': False,
            'version': None,
            'backend': None,
            'status': 'error'
        }
        
        try:
            import webview
            result['available'] = True
            
            # 获取版本信息
            if hasattr(webview, '__version__'):
                result['version'] = webview.__version__
            else:
                # 尝试从包信息获取版本
                try:
                    import pkg_resources
                    result['version'] = pkg_resources.get_distribution('pywebview').version
                except:
                    result['version'] = 'unknown'
            
            # 检查API兼容性
            compatibility_checks = {
                'create_window': hasattr(webview, 'create_window'),
                'start': hasattr(webview, 'start'),
                'FileDialog': hasattr(webview, 'FileDialog'),
                'FOLDER_DIALOG': hasattr(webview, 'FOLDER_DIALOG')
            }
            
            result['api_compatibility'] = compatibility_checks
            
            # 检查后端可用性
            backends = []
            if platform.system() == 'Windows':
                try:
                    # 检查WebView2
                    backends.append('webview2')
                except:
                    pass
            
            result['available_backends'] = backends
            result['status'] = 'ok'
            
            # 版本兼容性警告
            if result['version']:
                try:
                    version_parts = result['version'].split('.')
                    major_version = int(version_parts[0])
                    if major_version >= 6:
                        self.warnings.append("pywebview 6.x可能存在兼容性问题，建议使用4.x-5.x版本")
                    elif major_version == 5:
                        # 5.x版本是推荐的稳定版本，无需警告
                        pass
                    elif major_version == 4:
                        # 4.x版本也是兼容的，无需警告
                        pass
                    else:
                        self.warnings.append(f"pywebview {result['version']}版本较旧，建议升级到5.x版本")
                except (ValueError, IndexError):
                    pass
            
        except ImportError:
            self.issues.append("pywebview未安装")
        except Exception as e:
            self.issues.append(f"pywebview检查失败: {e}")
        
        return result
    
    def check_dependencies(self) -> Dict[str, Any]:
        """检查关键依赖"""
        dependencies = {
            'paddleocr': {'required': True, 'status': 'missing'},
            'paddle': {'required': True, 'status': 'missing'},
            'cv2': {'required': True, 'status': 'missing'},
            'PIL': {'required': True, 'status': 'missing'},
            'numpy': {'required': True, 'status': 'missing'},
            'psutil': {'required': False, 'status': 'missing'},
        }
        
        for dep_name, dep_info in dependencies.items():
            try:
                __import__(dep_name)
                dep_info['status'] = 'available'
            except ImportError:
                if dep_info['required']:
                    self.issues.append(f"缺少必需依赖: {dep_name}")
                else:
                    self.warnings.append(f"缺少可选依赖: {dep_name}")
        
        return dependencies
    
    def check_system_resources(self) -> Dict[str, Any]:
        """检查系统资源"""
        result = {
            'memory_gb': 0,
            'disk_space_gb': 0,
            'cpu_cores': 0,
            'status': 'ok'
        }
        
        try:
            import psutil
            
            # 内存检查
            memory = psutil.virtual_memory()
            result['memory_gb'] = memory.total / (1024**3)
            
            if result['memory_gb'] < 4:
                self.warnings.append(f"内存较少: {result['memory_gb']:.1f}GB (建议 >= 4GB)")
            
            # 磁盘空间检查
            disk = psutil.disk_usage('.')
            result['disk_space_gb'] = disk.free / (1024**3)
            
            if result['disk_space_gb'] < 2:
                self.warnings.append(f"磁盘空间不足: {result['disk_space_gb']:.1f}GB (建议 >= 2GB)")
            
            # CPU检查
            result['cpu_cores'] = psutil.cpu_count()
            
            if result['cpu_cores'] < 2:
                self.warnings.append(f"CPU核心较少: {result['cpu_cores']} (建议 >= 2)")
                
        except ImportError:
            self.warnings.append("无法检查系统资源 (psutil未安装)")
        except Exception as e:
            self.warnings.append(f"系统资源检查失败: {e}")
        
        return result
    
    def generate_report(self) -> str:
        """生成兼容性检查报告"""
        results = self.check_all()
        
        report = []
        report.append("=" * 60)
        report.append("Prisma OCR 系统兼容性检查报告")
        report.append("=" * 60)
        
        # 整体状态
        status_emoji = {
            'ok': '✅',
            'warning': '⚠️',
            'error': '❌',
            'unknown': '❓'
        }
        
        report.append(f"\n整体状态: {status_emoji.get(results['overall_status'], '❓')} {results['overall_status'].upper()}")
        
        # Python版本
        py_info = results['python_version']
        report.append(f"\nPython版本: {py_info['version']} {status_emoji.get(py_info['status'], '❓')}")
        
        # 平台信息
        platform_info = results['platform_info']
        report.append(f"操作系统: {platform_info['system']} {platform_info['release']}")
        
        # pywebview状态
        webview_info = results['pywebview_compatibility']
        webview_status = status_emoji.get(webview_info['status'], '❓')
        report.append(f"pywebview: {webview_info.get('version', 'N/A')} {webview_status}")
        
        # 问题和警告
        if results['issues']:
            report.append(f"\n❌ 发现 {len(results['issues'])} 个问题:")
            for issue in results['issues']:
                report.append(f"  - {issue}")
        
        if results['warnings']:
            report.append(f"\n⚠️ 发现 {len(results['warnings'])} 个警告:")
            for warning in results['warnings']:
                report.append(f"  - {warning}")
        
        if not results['issues'] and not results['warnings']:
            report.append("\n🎉 系统兼容性良好，可以正常运行!")
        
        report.append("\n" + "=" * 60)
        
        return "\n".join(report)


def run_compatibility_check() -> Dict[str, Any]:
    """运行兼容性检查"""
    checker = CompatibilityChecker()
    return checker.check_all()


def print_compatibility_report():
    """打印兼容性检查报告"""
    checker = CompatibilityChecker()
    print(checker.generate_report())


if __name__ == "__main__":
    print_compatibility_report()
