#!/usr/bin/env python3
"""
OCR功能最终测试
验证修复后的OCR功能
"""

import sys
import os
import time
import shutil
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def setup_test_environment():
    """设置测试环境"""
    print("设置测试环境...")
    
    # 复制测试文件到临时目录
    temp_dir = Path(tempfile.mkdtemp(prefix="ocr_final_test_"))
    test_files = {}
    
    # 复制PNG文件
    png_source = project_root / "ocr测试.PNG"
    if png_source.exists():
        png_dest = temp_dir / "test_image.png"
        shutil.copy2(png_source, png_dest)
        test_files['png'] = png_dest
        print(f"[INFO] PNG文件: {png_dest}")
    
    # 复制PDF文件
    pdf_source = project_root / "ocr测试.pdf"
    if pdf_source.exists():
        pdf_dest = temp_dir / "test_document.pdf"
        shutil.copy2(pdf_source, pdf_dest)
        test_files['pdf'] = pdf_dest
        print(f"[INFO] PDF文件: {pdf_dest}")
    
    return temp_dir, test_files


def test_paddleocr_fixed():
    """测试修复后的PaddleOCR"""
    print("\n测试修复后的PaddleOCR...")
    
    try:
        from paddleocr import PaddleOCR
        import numpy as np
        
        # 创建PaddleOCR实例（使用新API）
        print("[INFO] 创建PaddleOCR实例...")
        ocr = PaddleOCR(
            use_textline_orientation=True,  # 使用新参数名
            lang='ch'
        )
        print("[PASS] PaddleOCR实例创建成功")
        
        # 创建测试图像
        test_image = np.ones((100, 300, 3), dtype=np.uint8) * 255
        
        # 测试OCR识别（使用新API）
        print("[INFO] 测试OCR识别...")
        start_time = time.time()
        result = ocr.predict(test_image)  # 使用predict而不是ocr
        end_time = time.time()
        
        print(f"[PASS] PaddleOCR识别完成，耗时: {end_time - start_time:.2f}s")
        
        if result and result[0]:
            print(f"  识别结果数量: {len(result[0])}")
        else:
            print("  [INFO] 空白图像未识别到内容（正常）")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] PaddleOCR测试失败: {e}")
        return False


def test_ocr_engine_fixed():
    """测试修复后的OCR引擎"""
    print("\n测试修复后的OCR引擎...")
    
    try:
        from core.ocr_engine import OCREngine
        from config.settings import AppSettings
        from config.models_config import ModelsConfig
        
        # 创建配置
        settings = AppSettings()
        models_config = ModelsConfig()
        
        # 创建OCR引擎
        ocr_engine = OCREngine(settings.ocr, models_config)
        print("[PASS] OCR引擎实例创建成功")
        
        # 设置回调函数
        progress_updates = []
        error_messages = []
        
        def progress_callback(progress, status):
            progress_updates.append((progress, status))
            print(f"  进度: {progress}% - {status}")
        
        def error_callback(error_msg):
            error_messages.append(error_msg)
            print(f"  错误: {error_msg}")
        
        ocr_engine.progress_callback = progress_callback
        ocr_engine.error_callback = error_callback
        
        # 尝试初始化
        print("[INFO] 开始初始化OCR引擎...")
        start_time = time.time()
        
        success = ocr_engine.initialize()
        end_time = time.time()
        
        if success:
            print(f"[PASS] OCR引擎初始化成功，耗时: {end_time - start_time:.1f}s")
            print(f"  进度更新次数: {len(progress_updates)}")
            return ocr_engine
        else:
            print(f"[FAIL] OCR引擎初始化失败，耗时: {end_time - start_time:.1f}s")
            if error_messages:
                print(f"  最后错误: {error_messages[-1]}")
            return None
        
    except Exception as e:
        print(f"[FAIL] OCR引擎测试异常: {e}")
        return None


def test_real_ocr_processing(ocr_engine, test_files):
    """测试真实OCR处理"""
    print("\n测试真实OCR处理...")
    
    if not ocr_engine:
        print("[SKIP] OCR引擎未初始化")
        return True
    
    if 'png' not in test_files:
        print("[SKIP] 没有图像测试文件")
        return True
    
    try:
        from utils.image_utils import ImageUtils
        
        image_path = test_files['png']
        
        # 加载图像
        pil_image = ImageUtils.load_image_pil(image_path)
        print(f"[INFO] 处理图像: {image_path.name}，尺寸: {pil_image.size}")
        
        # 测试通用模式OCR
        print("测试通用模式OCR...")
        ocr_engine.set_mode('general')
        
        start_time = time.time()
        result = ocr_engine.process_image(pil_image, page_number=1)
        end_time = time.time()
        
        print(f"[PASS] 通用模式OCR完成，耗时: {end_time - start_time:.2f}s")
        
        # 分析结果
        if hasattr(result, 'text_blocks') and result.text_blocks:
            print(f"  识别到 {len(result.text_blocks)} 个文本块")
            
            # 显示识别结果
            total_text = ""
            for i, block in enumerate(result.text_blocks[:5]):  # 显示前5个
                text = block.get('text', '').strip()
                confidence = block.get('confidence', 0.0)
                if text:
                    print(f"    {i+1}. {text[:50]}... (置信度: {confidence:.2f})")
                    total_text += text + " "
            
            if len(result.text_blocks) > 5:
                print(f"    ... 还有 {len(result.text_blocks) - 5} 个文本块")
            
            print(f"  总文本长度: {len(total_text)} 字符")
            
            # 验证OCR质量
            if len(total_text.strip()) > 10:
                print("[PASS] OCR识别到有效文本内容")
            else:
                print("[WARN] OCR识别的文本内容较少")
        else:
            print("[WARN] 未识别到文本内容")
        
        # 测试表格模式OCR
        print("测试表格模式OCR...")
        ocr_engine.set_mode('table')
        
        start_time = time.time()
        table_result = ocr_engine.process_image(pil_image, page_number=1)
        end_time = time.time()
        
        print(f"[PASS] 表格模式OCR完成，耗时: {end_time - start_time:.2f}s")
        
        # 分析表格结果
        if hasattr(table_result, 'text_blocks') and table_result.text_blocks:
            print(f"  识别到 {len(table_result.text_blocks)} 个文本块")
        
        if hasattr(table_result, 'tables') and table_result.tables:
            print(f"  识别到 {len(table_result.tables)} 个表格")
        else:
            print("  未识别到表格结构")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] 真实OCR处理测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_performance_metrics(ocr_engine, test_files):
    """测试性能指标"""
    print("\n测试性能指标...")
    
    if not ocr_engine or 'png' not in test_files:
        print("[SKIP] 跳过性能测试")
        return True
    
    try:
        from utils.image_utils import ImageUtils
        import psutil
        
        image_path = test_files['png']
        pil_image = ImageUtils.load_image_pil(image_path)
        
        # 记录初始内存
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 多次OCR测试
        ocr_times = []
        ocr_engine.set_mode('general')
        
        for i in range(3):
            print(f"  性能测试 {i+1}/3...")
            start_time = time.time()
            result = ocr_engine.process_image(pil_image, page_number=1)
            end_time = time.time()
            
            ocr_time = end_time - start_time
            ocr_times.append(ocr_time)
            print(f"    耗时: {ocr_time:.2f}s")
        
        # 计算性能指标
        avg_time = sum(ocr_times) / len(ocr_times)
        min_time = min(ocr_times)
        max_time = max(ocr_times)
        
        print(f"[PASS] 性能指标:")
        print(f"  平均耗时: {avg_time:.2f}s")
        print(f"  最快耗时: {min_time:.2f}s")
        print(f"  最慢耗时: {max_time:.2f}s")
        
        # 检查内存使用
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        print(f"  内存增加: {memory_increase:.1f} MB")
        
        # 性能评估
        if avg_time < 10:
            print("  [EXCELLENT] OCR性能优秀")
        elif avg_time < 20:
            print("  [GOOD] OCR性能良好")
        elif avg_time < 30:
            print("  [FAIR] OCR性能一般")
        else:
            print("  [POOR] OCR性能较慢")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] 性能测试异常: {e}")
        return False


def main():
    """主测试函数"""
    print("Prisma OCR功能最终测试")
    print("=" * 60)
    print("验证修复后的OCR功能")
    
    # 设置测试环境
    temp_dir, test_files = setup_test_environment()
    
    if not test_files:
        print("[ERROR] 没有找到测试文件")
        return 1
    
    try:
        test_results = []
        
        # 1. 测试修复后的PaddleOCR
        print(f"\n{'='*20} PaddleOCR修复测试 {'='*20}")
        result = test_paddleocr_fixed()
        test_results.append(("PaddleOCR修复", result))
        
        # 2. 测试修复后的OCR引擎
        print(f"\n{'='*20} OCR引擎修复测试 {'='*20}")
        ocr_engine = test_ocr_engine_fixed()
        test_results.append(("OCR引擎初始化", ocr_engine is not None))
        
        # 3. 测试真实OCR处理
        if ocr_engine:
            print(f"\n{'='*20} 真实OCR处理测试 {'='*20}")
            result = test_real_ocr_processing(ocr_engine, test_files)
            test_results.append(("真实OCR处理", result))
            
            # 4. 测试性能指标
            print(f"\n{'='*20} 性能指标测试 {'='*20}")
            result = test_performance_metrics(ocr_engine, test_files)
            test_results.append(("性能指标", result))
        
        # 生成最终报告
        print(f"\n{'='*60}")
        print("最终测试报告")
        print(f"{'='*60}")
        
        passed = sum(1 for _, success in test_results if success)
        total = len(test_results)
        
        print(f"总测试数: {total}")
        print(f"通过: {passed}")
        print(f"失败: {total - passed}")
        print(f"成功率: {(passed/total*100):.1f}%")
        
        print(f"\n详细结果:")
        for test_name, success in test_results:
            status = "[PASS]" if success else "[FAIL]"
            print(f"  {status} {test_name}")
        
        # 最终结论
        print(f"\n最终结论:")
        if passed == total:
            print("🎉 [SUCCESS] 所有测试通过！")
            print("   - OCR引擎已成功修复并正常工作")
            print("   - 系统已准备好进行OCR处理")
            print("   - 可以开始使用OCR功能")
        elif passed >= total * 0.75:
            print("✅ [GOOD] 大部分测试通过！")
            print("   - OCR引擎基本正常工作")
            print("   - 建议解决剩余问题以获得最佳性能")
        else:
            print("❌ [ERROR] 多数测试失败")
            print("   - OCR引擎仍存在问题")
            print("   - 需要进一步调试和修复")
        
        return 0 if passed >= total * 0.75 else 1
        
    finally:
        # 清理临时文件
        try:
            shutil.rmtree(temp_dir)
            print(f"\n[INFO] 临时文件已清理")
        except Exception as e:
            print(f"\n[WARN] 清理临时文件失败: {e}")


if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程发生未知错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
