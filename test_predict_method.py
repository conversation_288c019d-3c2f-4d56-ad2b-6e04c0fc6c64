#!/usr/bin/env python3
"""
测试PaddleOCR的predict方法
"""

from utils.image_utils import ImageUtils
import time
import numpy as np

def test_predict_method():
    print("=== 测试PaddleOCR的predict方法 ===")
    
    # 测试图像
    test_image = 'ocr测试.PNG'
    
    # 加载图像
    print(f"正在加载图像: {test_image}")
    image = ImageUtils.load_image_pil(test_image)
    img_array = np.array(image)
    
    # 测试不同的配置
    configs = [
        {
            'name': '默认配置',
            'use_textline_orientation': True,
            'lang': 'ch'
        },
        {
            'name': '敏感检测',
            'use_textline_orientation': True,
            'lang': 'ch',
            'text_det_thresh': 0.1,
            'text_det_box_thresh': 0.3
        },
        {
            'name': '超敏感检测',
            'use_textline_orientation': True,
            'lang': 'ch',
            'text_det_thresh': 0.01,
            'text_det_box_thresh': 0.1
        }
    ]
    
    for config in configs:
        print(f"\n测试配置: {config['name']}")
        for key, value in config.items():
            if key != 'name':
                print(f"  {key}: {value}")
        
        try:
            from paddleocr import PaddleOCR
            
            # 创建OCR实例
            temp_ocr = PaddleOCR(**{k: v for k, v in config.items() if k != 'name'})
            
            # 使用predict方法
            start_time = time.time()
            result = temp_ocr.predict(img_array)
            end_time = time.time()
            
            print(f"  处理时间: {end_time - start_time:.2f}s")
            print(f"  结果类型: {type(result)}")
            
            # 分析结果
            if result:
                print(f"  结果长度: {len(result)}")
                
                # 检查结果结构
                if isinstance(result, list) and len(result) > 0:
                    first_item = result[0]
                    print(f"  第一项类型: {type(first_item)}")
                    
                    if isinstance(first_item, dict):
                        print(f"  字典键: {list(first_item.keys())}")
                        
                        # 查找文本相关的键
                        for key, value in first_item.items():
                            print(f"    {key}: {type(value)} - {str(value)[:100]}")
                    
                    elif isinstance(first_item, list):
                        print(f"  列表长度: {len(first_item)}")
                        if len(first_item) > 0:
                            print(f"  列表第一项: {type(first_item[0])} - {str(first_item[0])[:100]}")
                    
                    else:
                        print(f"  内容: {str(first_item)[:200]}")
                
                print(f"  ✓ predict方法执行成功")
            else:
                print("  ✗ predict方法返回空结果")
                
        except Exception as e:
            print(f"  ✗ 配置测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 测试最简单的配置
    print(f"\n=== 测试最简单配置 ===")
    
    try:
        from paddleocr import PaddleOCR
        
        # 最简单的配置
        simple_ocr = PaddleOCR(lang='ch')
        
        start_time = time.time()
        result = simple_ocr.predict(img_array)
        end_time = time.time()
        
        print(f"处理时间: {end_time - start_time:.2f}s")
        print(f"结果: {result}")
        
        if result:
            print(f"✓ 简单配置成功，结果类型: {type(result)}")
            
            # 尝试解析结果
            if isinstance(result, list) and len(result) > 0:
                for i, item in enumerate(result[:3]):
                    print(f"  项目 {i}: {type(item)} - {str(item)[:100]}")
        else:
            print("✗ 简单配置返回空结果")
            
    except Exception as e:
        print(f"✗ 简单配置测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试旧的ocr方法（不带cls参数）
    print(f"\n=== 测试旧的ocr方法（不带cls参数）===")
    
    try:
        from paddleocr import PaddleOCR
        
        ocr = PaddleOCR(lang='ch')
        
        start_time = time.time()
        result = ocr.ocr(img_array)  # 不带cls参数
        end_time = time.time()
        
        print(f"处理时间: {end_time - start_time:.2f}s")
        
        if result:
            print(f"✓ ocr方法成功，结果类型: {type(result)}")
            print(f"结果长度: {len(result)}")
            
            # 检查是否有文本块
            if isinstance(result, list) and len(result) > 0:
                page_result = result[0]  # 第一页的结果
                if page_result:
                    print(f"✓ 识别到 {len(page_result)} 个文本块")
                    
                    for i, block in enumerate(page_result[:5]):
                        if len(block) >= 2:
                            text = block[1][0] if block[1] else ""
                            confidence = block[1][1] if len(block[1]) > 1 else 0.0
                            print(f"  {i+1}. {text} (置信度: {confidence:.3f})")
                            
                    if len(page_result) > 0:
                        print(f"🎉 成功识别到文本！")
                else:
                    print("✗ 第一页结果为空")
            else:
                print("✗ 结果格式不正确")
        else:
            print("✗ ocr方法返回空结果")
            
    except Exception as e:
        print(f"✗ ocr方法测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_predict_method()
