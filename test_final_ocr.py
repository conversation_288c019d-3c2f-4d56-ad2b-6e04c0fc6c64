#!/usr/bin/env python3
"""
最终OCR测试
"""

from core.ocr_engine import OCREngine
from config.settings import AppSettings
from config.models_config import ModelsConfig
from utils.image_utils import ImageUtils
import time

def test_final_ocr():
    print("=== 最终OCR测试 ===")
    
    # 初始化OCR引擎
    settings = AppSettings()
    models_config = ModelsConfig()
    ocr_engine = OCREngine(settings.ocr, models_config)
    
    print("正在初始化OCR引擎...")
    if not ocr_engine.initialize():
        print("✗ OCR引擎初始化失败")
        return
    
    print("✓ OCR引擎初始化成功")
    
    # 测试图像
    test_image = 'ocr测试.PNG'
    print(f"\n正在处理图像: {test_image}")
    
    try:
        image = ImageUtils.load_image_pil(test_image)
        print(f"图像模式: {image.mode}, 尺寸: {image.size}")
        
        start_time = time.time()
        result = ocr_engine.process_image(image, page_number=1)
        end_time = time.time()
        
        print(f"处理完成，耗时: {end_time - start_time:.2f}s")
        print(f"识别到的文本块数: {len(result.text_blocks)}")
        
        if result.text_blocks:
            print("\n✓ 成功识别到文本！")
            print("识别结果:")
            
            for i, block in enumerate(result.text_blocks):
                text = block.get('text', '').strip()
                confidence = block.get('confidence', 0.0)
                print(f"  {i+1:2d}. {text} (置信度: {confidence:.3f})")
            
            # 合并所有文本
            all_text = '\n'.join([block.get('text', '').strip() for block in result.text_blocks])
            print(f"\n=== 合并文本 ===")
            print(all_text)
            
            print(f"\n🎉 OCR修复成功！识别到 {len(result.text_blocks)} 个文本块")
            
        else:
            print("✗ 未识别到文本内容")
            
    except Exception as e:
        print(f"✗ 处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_final_ocr()
