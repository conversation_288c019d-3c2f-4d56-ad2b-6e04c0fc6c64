#!/usr/bin/env python3
"""
测试新的PaddleOCR API参数
"""

from utils.image_utils import ImageUtils
import time
import numpy as np

def test_new_api_parameters():
    print("=== 测试新的PaddleOCR API参数 ===")
    
    # 测试图像
    test_image = 'ocr测试.PNG'
    
    # 加载图像
    print(f"正在加载图像: {test_image}")
    image = ImageUtils.load_image_pil(test_image)
    img_array = np.array(image)
    
    # 新API参数配置
    new_configs = [
        {
            'name': '默认配置',
            'use_textline_orientation': True,
            'lang': 'ch'
        },
        {
            'name': '敏感检测',
            'use_textline_orientation': True,
            'lang': 'ch',
            'text_det_thresh': 0.1,
            'text_det_box_thresh': 0.3,
            'text_recognition_batch_size': 1
        },
        {
            'name': '超敏感检测',
            'use_textline_orientation': True,
            'lang': 'ch',
            'text_det_thresh': 0.01,
            'text_det_box_thresh': 0.1,
            'text_recognition_batch_size': 1
        },
        {
            'name': '最低阈值',
            'use_textline_orientation': True,
            'lang': 'ch',
            'text_det_thresh': 0.001,
            'text_det_box_thresh': 0.01,
            'text_recognition_batch_size': 1
        }
    ]
    
    for config in new_configs:
        print(f"\n测试配置: {config['name']}")
        for key, value in config.items():
            if key != 'name':
                print(f"  {key}: {value}")
        
        try:
            from paddleocr import PaddleOCR
            
            # 创建OCR实例
            temp_ocr = PaddleOCR(**{k: v for k, v in config.items() if k != 'name'})
            
            # 执行OCR
            start_time = time.time()
            result = temp_ocr.ocr(img_array, cls=True)
            end_time = time.time()
            
            print(f"  处理时间: {end_time - start_time:.2f}s")
            
            # 分析结果
            if result and result[0]:
                text_blocks = result[0]
                print(f"  ✓ 识别到 {len(text_blocks)} 个文本块")
                
                for i, block in enumerate(text_blocks[:5]):
                    if len(block) >= 2:
                        text = block[1][0] if block[1] else ""
                        confidence = block[1][1] if len(block[1]) > 1 else 0.0
                        print(f"    {i+1}. {text} (置信度: {confidence:.3f})")
                        
                if len(text_blocks) > 0:
                    print(f"  🎉 成功！找到了有效的配置")
                    return config  # 返回成功的配置
            else:
                print("  ✗ 未识别到文本内容")
                
        except Exception as e:
            print(f"  ✗ 配置测试失败: {e}")
    
    # 测试不同语言
    print(f"\n=== 测试不同语言设置 ===")
    
    languages = ['ch', 'en', 'chinese_cht']
    
    for lang in languages:
        print(f"\n测试语言: {lang}")
        
        try:
            from paddleocr import PaddleOCR
            
            config = {
                'use_textline_orientation': True,
                'lang': lang,
                'text_det_thresh': 0.1,
                'text_det_box_thresh': 0.3
            }
            
            temp_ocr = PaddleOCR(**config)
            
            start_time = time.time()
            result = temp_ocr.ocr(img_array, cls=True)
            end_time = time.time()
            
            print(f"  处理时间: {end_time - start_time:.2f}s")
            
            if result and result[0]:
                text_blocks = result[0]
                print(f"  ✓ 识别到 {len(text_blocks)} 个文本块")
                
                for i, block in enumerate(text_blocks[:3]):
                    if len(block) >= 2:
                        text = block[1][0] if block[1] else ""
                        confidence = block[1][1] if len(block[1]) > 1 else 0.0
                        print(f"    {i+1}. {text} (置信度: {confidence:.3f})")
                        
                if len(text_blocks) > 0:
                    print(f"  🎉 语言 {lang} 成功识别文本！")
            else:
                print("  ✗ 未识别到文本内容")
                
        except Exception as e:
            print(f"  ✗ 语言测试失败: {e}")
    
    # 测试最简单的配置
    print(f"\n=== 测试最简单配置 ===")
    
    try:
        from paddleocr import PaddleOCR
        
        # 最简单的配置
        simple_ocr = PaddleOCR(lang='ch')
        
        start_time = time.time()
        result = simple_ocr.ocr(img_array)
        end_time = time.time()
        
        print(f"处理时间: {end_time - start_time:.2f}s")
        
        if result and result[0]:
            text_blocks = result[0]
            print(f"✓ 识别到 {len(text_blocks)} 个文本块")
            
            for i, block in enumerate(text_blocks[:5]):
                if len(block) >= 2:
                    text = block[1][0] if block[1] else ""
                    confidence = block[1][1] if len(block[1]) > 1 else 0.0
                    print(f"  {i+1}. {text} (置信度: {confidence:.3f})")
                    
            if len(text_blocks) > 0:
                print(f"🎉 最简单配置成功！")
        else:
            print("✗ 未识别到文本内容")
            
    except Exception as e:
        print(f"✗ 简单配置测试失败: {e}")
    
    print(f"\n=== 测试完成 ===")

if __name__ == "__main__":
    test_new_api_parameters()
