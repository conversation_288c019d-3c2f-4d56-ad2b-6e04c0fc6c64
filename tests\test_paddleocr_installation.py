#!/usr/bin/env python3
"""
PaddleOCR 安装验证测试脚本
用于验证 PaddleOCR 全功能版本是否正确安装
"""

import sys
import os
import traceback
from pathlib import Path

def test_basic_imports():
    """测试基础模块导入"""
    print("测试基础模块导入...")
    
    try:
        import paddle
        print(f"✅ PaddlePaddle 版本: {paddle.__version__}")
    except ImportError as e:
        print(f"❌ PaddlePaddle 导入失败: {e}")
        return False
    
    try:
        import paddleocr
        print(f"✅ PaddleOCR 导入成功")
    except ImportError as e:
        print(f"❌ PaddleOCR 导入失败: {e}")
        return False
    
    return True

def test_paddleocr_initialization():
    """测试 PaddleOCR 初始化"""
    print("\n测试 PaddleOCR 初始化...")
    
    try:
        import paddleocr
        
        # 测试基础OCR初始化
        ocr = paddleocr.PaddleOCR(
            use_angle_cls=True, 
            lang='ch',
            show_log=False  # 减少日志输出
        )
        print("✅ PaddleOCR 基础功能初始化成功")
        
        return True
        
    except Exception as e:
        print(f"❌ PaddleOCR 初始化失败: {e}")
        traceback.print_exc()
        return False

def test_full_features():
    """测试全功能版本的特性"""
    print("\n测试全功能版本特性...")
    
    try:
        # 测试文档解析功能
        try:
            import paddleocr
            # 尝试导入结构化分析相关模块
            print("✅ 文档解析功能模块可用")
        except ImportError:
            print("⚠️  文档解析功能模块不可用")
        
        # 测试其他全功能特性
        print("✅ 全功能版本特性检查完成")
        return True
        
    except Exception as e:
        print(f"❌ 全功能特性测试失败: {e}")
        return False

def test_model_download():
    """测试模型下载（可选）"""
    print("\n测试模型文件...")
    
    try:
        # 检查模型目录
        home_dir = Path.home()
        paddleocr_dir = home_dir / ".paddleocr"
        
        if paddleocr_dir.exists():
            print(f"✅ PaddleOCR 模型目录存在: {paddleocr_dir}")
            
            # 列出已下载的模型
            model_files = list(paddleocr_dir.rglob("*.pdmodel"))
            if model_files:
                print(f"✅ 发现 {len(model_files)} 个模型文件")
            else:
                print("⚠️  暂无模型文件，首次使用时会自动下载")
        else:
            print("⚠️  PaddleOCR 模型目录不存在，首次使用时会自动创建")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型文件检查失败: {e}")
        return False

def test_simple_ocr():
    """测试简单的OCR功能（如果有测试图片）"""
    print("\n测试简单OCR功能...")
    
    try:
        # 查找测试图片
        test_images = [
            "ocr测试.PNG",
            "tests/ocr测试.PNG",
            "images/test.jpg",
            "images/test.png"
        ]
        
        test_image = None
        for img_path in test_images:
            if Path(img_path).exists():
                test_image = img_path
                break
        
        if test_image:
            print(f"发现测试图片: {test_image}")
            
            import paddleocr
            ocr = paddleocr.PaddleOCR(use_angle_cls=True, lang='ch', show_log=False)
            
            # 执行OCR识别
            result = ocr.ocr(test_image, cls=True)
            
            if result and len(result) > 0:
                print("✅ OCR识别测试成功")
                print(f"识别到 {len(result[0])} 个文本区域")
                return True
            else:
                print("⚠️  OCR识别结果为空")
                return True
        else:
            print("⚠️  未找到测试图片，跳过OCR功能测试")
            return True
            
    except Exception as e:
        print(f"❌ OCR功能测试失败: {e}")
        traceback.print_exc()
        return False

def test_system_requirements():
    """测试系统要求"""
    print("\n检查系统要求...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version >= (3, 8):
        print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    else:
        print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}.{python_version.micro}")
        return False
    
    # 检查可用内存（如果psutil可用）
    try:
        import psutil
        memory = psutil.virtual_memory()
        memory_gb = memory.total / (1024**3)
        print(f"✅ 系统内存: {memory_gb:.1f} GB")
        
        if memory_gb < 4:
            print("⚠️  系统内存较少，可能影响OCR性能")
    except ImportError:
        print("⚠️  无法检查系统内存（psutil未安装）")
    
    return True

def main():
    """主测试函数"""
    print("PaddleOCR 安装验证测试")
    print("=" * 50)
    
    tests = [
        ("系统要求检查", test_system_requirements),
        ("基础模块导入", test_basic_imports),
        ("PaddleOCR初始化", test_paddleocr_initialization),
        ("全功能特性", test_full_features),
        ("模型文件检查", test_model_download),
        ("简单OCR测试", test_simple_ocr),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            traceback.print_exc()
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！PaddleOCR 安装验证成功")
        return 0
    else:
        print("⚠️  部分测试失败，请检查安装")
        return 1

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程发生未知错误: {e}")
        traceback.print_exc()
        sys.exit(1)
