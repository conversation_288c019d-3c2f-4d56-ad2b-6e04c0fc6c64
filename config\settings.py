"""
应用程序设置管理
"""

import os
import toml
from pathlib import Path
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field
from dataclasses import dataclass


@dataclass
class OCRSettings:
    """OCR设置"""
    use_gpu: bool = True
    use_angle_cls: bool = True
    use_space_char: bool = True
    lang: str = 'ch'
    det_model_dir: Optional[str] = None
    rec_model_dir: Optional[str] = None
    cls_model_dir: Optional[str] = None

    # PP-DocTranslation配置（仅用于文档识别，不使用翻译功能）
    use_doc_translation: bool = False
    doc_translation_config: Dict[str, Any] = None

    def __post_init__(self):
        if self.doc_translation_config is None:
            # PP-DocTranslation配置 - 仅启用视觉识别功能
            # 注意：chat_bot_config 永远设置为 None，不使用翻译功能
            self.doc_translation_config = {
                'use_doc_orientation_classify': False,
                'use_doc_unwarping': False,
                'use_textline_orientation': True,
                'use_seal_recognition': True,
                'use_table_recognition': True,
                'use_formula_recognition': True,
                'use_chart_recognition': False,
                'use_region_detection': True,
                'device': 'cpu'
            }


@dataclass
class TranslationSettings:
    """翻译设置"""
    enabled: bool = False
    target_language: str = 'en'
    source_language: str = 'auto'
    preserve_layout: bool = True


@dataclass
class OutputSettings:
    """输出设置"""
    default_format: str = 'txt'
    output_directory: str = ''
    merge_tables: bool = False
    create_searchable_pdf: bool = True


@dataclass
class UISettings:
    """界面设置"""
    theme: str = 'dark'
    language: str = 'zh_CN'
    window_width: int = 1200
    window_height: int = 800
    remember_window_state: bool = True


class AppSettings:
    """应用程序设置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or self._get_default_config_path()
        self.ocr = OCRSettings()
        self.translation = TranslationSettings()
        self.output = OutputSettings()
        self.ui = UISettings()
        
        # 加载配置
        self.load()
    
    def _get_default_config_path(self) -> str:
        """获取默认配置文件路径"""
        config_dir = Path.home() / '.prisma_ocr'
        config_dir.mkdir(exist_ok=True)
        return str(config_dir / 'config.toml')
    
    def load(self) -> None:
        """加载配置文件"""
        if not os.path.exists(self.config_file):
            self.save()  # 创建默认配置文件
            return
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config_data = toml.load(f)
            
            # 更新OCR设置
            if 'ocr' in config_data:
                ocr_data = config_data['ocr']
                self.ocr.use_gpu = ocr_data.get('use_gpu', self.ocr.use_gpu)
                self.ocr.use_angle_cls = ocr_data.get('use_angle_cls', self.ocr.use_angle_cls)
                self.ocr.use_space_char = ocr_data.get('use_space_char', self.ocr.use_space_char)
                self.ocr.lang = ocr_data.get('lang', self.ocr.lang)
                self.ocr.det_model_dir = ocr_data.get('det_model_dir', self.ocr.det_model_dir)
                self.ocr.rec_model_dir = ocr_data.get('rec_model_dir', self.ocr.rec_model_dir)
                self.ocr.cls_model_dir = ocr_data.get('cls_model_dir', self.ocr.cls_model_dir)
            
            # 更新翻译设置
            if 'translation' in config_data:
                trans_data = config_data['translation']
                self.translation.enabled = trans_data.get('enabled', self.translation.enabled)
                self.translation.target_language = trans_data.get('target_language', self.translation.target_language)
                self.translation.source_language = trans_data.get('source_language', self.translation.source_language)
                self.translation.preserve_layout = trans_data.get('preserve_layout', self.translation.preserve_layout)
            
            # 更新输出设置
            if 'output' in config_data:
                output_data = config_data['output']
                self.output.default_format = output_data.get('default_format', self.output.default_format)
                self.output.output_directory = output_data.get('output_directory', self.output.output_directory)
                self.output.merge_tables = output_data.get('merge_tables', self.output.merge_tables)
                self.output.create_searchable_pdf = output_data.get('create_searchable_pdf', self.output.create_searchable_pdf)
            
            # 更新界面设置
            if 'ui' in config_data:
                ui_data = config_data['ui']
                self.ui.theme = ui_data.get('theme', self.ui.theme)
                self.ui.language = ui_data.get('language', self.ui.language)
                self.ui.window_width = ui_data.get('window_width', self.ui.window_width)
                self.ui.window_height = ui_data.get('window_height', self.ui.window_height)
                self.ui.remember_window_state = ui_data.get('remember_window_state', self.ui.remember_window_state)
                
        except Exception as e:
            print(f"加载配置文件失败: {e}")
    
    def save(self) -> None:
        """保存配置文件"""
        config_data = {
            'ocr': {
                'use_gpu': self.ocr.use_gpu,
                'use_angle_cls': self.ocr.use_angle_cls,
                'use_space_char': self.ocr.use_space_char,
                'lang': self.ocr.lang,
                'det_model_dir': self.ocr.det_model_dir,
                'rec_model_dir': self.ocr.rec_model_dir,
                'cls_model_dir': self.ocr.cls_model_dir,
            },
            'translation': {
                'enabled': self.translation.enabled,
                'target_language': self.translation.target_language,
                'source_language': self.translation.source_language,
                'preserve_layout': self.translation.preserve_layout,
            },
            'output': {
                'default_format': self.output.default_format,
                'output_directory': self.output.output_directory,
                'merge_tables': self.output.merge_tables,
                'create_searchable_pdf': self.output.create_searchable_pdf,
            },
            'ui': {
                'theme': self.ui.theme,
                'language': self.ui.language,
                'window_width': self.ui.window_width,
                'window_height': self.ui.window_height,
                'remember_window_state': self.ui.remember_window_state,
            }
        }
        
        try:
            # 确保配置目录存在
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                toml.dump(config_data, f)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def reset_to_defaults(self) -> None:
        """重置为默认设置"""
        self.ocr = OCRSettings()
        self.translation = TranslationSettings()
        self.output = OutputSettings()
        self.ui = UISettings()
        self.save()
