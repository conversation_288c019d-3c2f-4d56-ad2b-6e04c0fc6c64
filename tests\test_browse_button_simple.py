#!/usr/bin/env python3
"""
简化的浏览按钮功能测试
验证修复是否成功
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def test_html_modifications():
    """测试HTML修改是否正确"""
    print("检查HTML修改...")
    
    html_file = project_root / "PrismaUI.html"
    
    if not html_file.exists():
        print("[FAIL] HTML文件不存在")
        return False
    
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查修改项
    checks = [
        ('浏览按钮ID', 'id="browse-output-path"'),
        ('输出路径输入框ID', 'id="output-path"'),
        ('JavaScript变量声明', 'const browseOutputPathBtn = document.getElementById(\'browse-output-path\')'),
        ('JavaScript变量声明', 'const outputPathInput = document.getElementById(\'output-path\')'),
        ('事件监听器', 'browseOutputPathBtn.addEventListener(\'click\''),
        ('API调用', 'pywebview.api.select_output_folder()'),
        ('结果处理', 'outputPathInput.value = result.folder_path')
    ]
    
    results = []
    for check_name, check_text in checks:
        if check_text in content:
            print(f"[PASS] {check_name}")
            results.append(True)
        else:
            print(f"[FAIL] {check_name} - 未找到: {check_text}")
            results.append(False)
    
    return all(results)


def test_api_bridge_method():
    """测试API桥接方法"""
    print("\n检查API桥接方法...")
    
    try:
        from webui.api_bridge import APIBridge
        
        # 创建API桥接实例
        api_bridge = APIBridge()
        
        # 检查方法是否存在
        if hasattr(api_bridge, 'select_output_folder'):
            print("[PASS] select_output_folder方法存在")
            
            # 检查方法签名
            import inspect
            sig = inspect.signature(api_bridge.select_output_folder)
            print(f"[INFO] 方法签名: {sig}")
            
            return True
        else:
            print("[FAIL] select_output_folder方法不存在")
            return False
            
    except Exception as e:
        print(f"[FAIL] API桥接测试失败: {e}")
        return False


def test_web_controller_method():
    """测试Web控制器方法"""
    print("\n检查Web控制器方法...")
    
    try:
        from webui.web_controller import WebController
        
        # 创建Web控制器实例
        web_controller = WebController(debug=True)
        
        # 检查方法是否存在
        if hasattr(web_controller, 'show_folder_dialog'):
            print("[PASS] show_folder_dialog方法存在")
            
            # 检查方法签名
            import inspect
            sig = inspect.signature(web_controller.show_folder_dialog)
            print(f"[INFO] 方法签名: {sig}")
            
            return True
        else:
            print("[FAIL] show_folder_dialog方法不存在")
            return False
            
    except Exception as e:
        print(f"[FAIL] Web控制器测试失败: {e}")
        return False


def show_fix_summary():
    """显示修复总结"""
    print("\n" + "="*60)
    print("浏览按钮修复总结")
    print("="*60)
    
    print("\n🔧 已完成的修复:")
    print("1. HTML按钮添加ID: browse-output-path")
    print("2. JavaScript事件监听器已添加")
    print("3. API桥接方法 select_output_folder() 已实现")
    print("4. Web控制器 show_folder_dialog() 方法可用")
    
    print("\n📋 修复的工作流程:")
    print("用户点击浏览按钮 → JavaScript事件触发 → 调用API方法")
    print("→ 显示文件夹选择对话框 → 返回选择结果 → 更新输入框")
    
    print("\n✅ 预期结果:")
    print("- 点击浏览按钮将打开文件夹选择对话框")
    print("- 选择文件夹后，路径将自动填入输出路径输入框")
    print("- 如果用户取消选择，输入框保持不变")
    
    print("\n🚀 使用方法:")
    print("1. 启动应用程序: python main.py")
    print("2. 在配置选项中找到输出路径")
    print("3. 点击输出路径右侧的'浏览'按钮")
    print("4. 选择目标文件夹")
    print("5. 确认路径已自动填入")


def main():
    """主测试函数"""
    print("浏览按钮修复验证")
    print("=" * 40)
    
    test_results = []
    
    # 1. 测试HTML修改
    result = test_html_modifications()
    test_results.append(("HTML修改", result))
    
    # 2. 测试API桥接方法
    result = test_api_bridge_method()
    test_results.append(("API桥接方法", result))
    
    # 3. 测试Web控制器方法
    result = test_web_controller_method()
    test_results.append(("Web控制器方法", result))
    
    # 生成报告
    print(f"\n{'='*40}")
    print("验证结果")
    print(f"{'='*40}")
    
    passed = sum(1 for _, success in test_results if success)
    total = len(test_results)
    
    for test_name, success in test_results:
        status = "[PASS]" if success else "[FAIL]"
        print(f"{status} {test_name}")
    
    print(f"\n通过率: {passed}/{total} ({(passed/total*100):.1f}%)")
    
    if passed == total:
        print("\n🎉 所有验证通过！浏览按钮功能已成功修复！")
        show_fix_summary()
        return 0
    else:
        print(f"\n⚠️  {total-passed} 项验证失败，需要进一步检查")
        return 1


if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n验证被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n验证过程发生未知错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
